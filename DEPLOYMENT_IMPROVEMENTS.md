# Deployment Script Improvements

## Summary of Changes Made to `deploy_aws.sh`

### 1. Configurable Host Variable ✅

**Added:**
- `DEFAULT_HOST` variable at the top of the script
- Users can now set their EC2 instance IP directly in the script
- Command-line `--host` option becomes optional when `DEFAULT_HOST` is set
- Updated usage instructions to reflect this change

**Usage:**
```bash
# Set in script
DEFAULT_HOST="*******"

# Then deploy without --host
./deploy_aws.sh --key ~/.ssh/ctr-road-name-key --env aws-production
```

### 2. Fixed PM2 Deployment Issues ✅

**Problems Solved:**
- PM2 processes not stopping properly before deployment
- New code not being loaded due to PM2 caching
- Deployment failures when PM2 services are already running

**Improvements Made:**
- Added `stop_pm2_processes()` function with proper process management
- Added `--force-restart` option for complete PM2 restart (deletes and recreates process)
- Graceful stop by default, force restart when needed
- Clear PM2 logs before deployment to avoid confusion
- Better error handling for PM2 operations

**New Options:**
```bash
--force-restart    # Force complete PM2 restart (slower but more reliable)
```

### 3. Fixed Configuration File Loading ✅

**Problems Solved:**
- `aws-production.json` config not being used correctly
- NODE_ENV not properly set to match config file
- PM2 ecosystem configuration not optimized for AWS

**Improvements Made:**
- Automatically uses `ecosystem.aws.config.js` if available
- Ensures `config/aws-production.json` is copied and used
- Properly sets `NODE_ENV=aws-production` environment variable
- Verifies configuration file exists before starting application
- Creates fallback config from production.json if needed

**Configuration Verification:**
- Checks that config file exists on server
- Verifies NODE_ENV is correctly set in PM2
- Shows which configuration is being used

### 4. Added Deployment Verification ✅

**New Verification Steps:**
- **PM2 Status Check**: Verifies process is online
- **Configuration Verification**: Confirms correct config file is loaded
- **Application Response Test**: Tests HTTP endpoints with retries
- **Environment Verification**: Confirms NODE_ENV is set correctly
- **Detailed Logging**: Shows PM2 status and recent logs on failure

**Enhanced Health Checks:**
- Multiple retry attempts for application startup
- Tests both `/health` and root endpoints
- Detailed error reporting with log output
- Graceful failure handling with useful debugging info

### 5. Additional Improvements ✅

**Better Error Handling:**
- Each deployment step now has proper error checking
- Deployment stops immediately on failure with clear error messages
- Better logging throughout the process

**Enhanced User Experience:**
- More informative output with step-by-step progress
- Better usage instructions and examples
- Comprehensive deployment summary with useful commands
- Quick test commands provided

**File Management:**
- Copies `scripts/` directory if it exists
- Makes scripts executable automatically
- Proper file permissions handling
- Better backup process

## Updated User Data Script

**Fixed in `terraform/user_data.sh`:**
- Changed application directory to `/var/www/ctr-road-name` (matching deployment script)
- Uses `ubuntu` user instead of `ctrapp` (matching deployment script)
- Proper PM2 startup configuration for ubuntu user

## Usage Examples

### Basic Deployment
```bash
# Set DEFAULT_HOST in script, then:
./deploy_aws.sh --key ~/.ssh/ctr-road-name-key

# Or specify host each time:
./deploy_aws.sh --host ******* --key ~/.ssh/ctr-road-name-key
```

### Force Restart (for problematic deployments)
```bash
./deploy_aws.sh --key ~/.ssh/ctr-road-name-key --force-restart
```

### Different Environment
```bash
./deploy_aws.sh --key ~/.ssh/ctr-road-name-key --env production
```

### Skip Build (for config-only changes)
```bash
./deploy_aws.sh --key ~/.ssh/ctr-road-name-key --skip-build
```

## Troubleshooting

### If Deployment Fails
1. Check the detailed error output
2. Use `--force-restart` for stubborn PM2 issues
3. SSH to server and check logs: `pm2 logs roadname`
4. Verify configuration: `cat /var/www/ctr-road-name/config/aws-production.json`

### Common Issues Resolved
- ✅ PM2 processes not restarting properly
- ✅ Old code being served after deployment
- ✅ Configuration files not being loaded
- ✅ Environment variables not set correctly
- ✅ Deployment hanging on PM2 operations

## Testing the Improvements

The script now includes comprehensive verification that ensures:
1. New code is actually deployed and running
2. Correct configuration file is being used
3. Application is responding to requests
4. PM2 process is healthy and online
5. Environment is correctly configured

This makes the deployment process much more reliable for iterative development where code changes need to be deployed multiple times to a running server.