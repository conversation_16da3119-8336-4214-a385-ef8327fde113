#!/usr/bin/env bash
SERVER="cdas_prod_ets_redis_0"
BUILD_PATH="build"
DEPLOY_PATH="server"

yarn
yarn compile
rm -rf $BUILD_PATH
mkdir $BUILD_PATH
cp package.json $BUILD_PATH/
cp ecosystem.config.js $BUILD_PATH/
cp -r lib $BUILD_PATH/
cp -r config $BUILD_PATH/
cp -r public $BUILD_PATH/
# sync build
rsync -avuz $BUILD_PATH/* $SERVER:$DEPLOY_PATH
rm -rf $BUILD_PATH
ssh $SERVER "cd $DEPLOY_PATH && pm2 restart ecosystem.config.js && pm2 logs"
echo "Done"
