#!/usr/bin/env bash

# Simple Redis Import Script
# Imports Redis dump file to destination server using a straightforward approach

set -euo pipefail

DEST_HOST="ctr-road-name"
DEST_PORT="6379"
DEST_DB="0"
DUMP_FILE="redis-migration-backup/redis_dump_20250728_104219.txt"

echo "Starting Redis import..."
echo "Destination: ${DEST_HOST}:${DEST_PORT} (DB ${DEST_DB})"
echo "Dump file: ${DUMP_FILE}"

# Check if dump file exists
if [ ! -f "${DUMP_FILE}" ]; then
    echo "Error: Dump file not found: ${DUMP_FILE}"
    exit 1
fi

# Get total commands (excluding comments)
total_commands=$(grep -v '^#' "${DUMP_FILE}" | wc -l)
echo "Total commands to import: ${total_commands}"

# Test connection
echo "Testing connection to destination server..."
if ! ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} ping" &>/dev/null; then
    echo "Error: Cannot connect to Redis on ${DEST_HOST}:${DEST_PORT}"
    exit 1
fi
echo "Connection successful!"

# Copy dump file to destination server
echo "Copying dump file to destination server..."
scp "${DUMP_FILE}" "${DEST_HOST}:/tmp/redis_import.txt"

# Create import script on destination server
echo "Creating import script on destination server..."
ssh "${DEST_HOST}" 'cat > /tmp/import_redis.sh << '\''EOF'\''
#!/bin/bash
PORT=$1
DB=$2
FILE=$3

echo "Starting import on destination server..."
total_lines=$(grep -v "^#" "$FILE" | wc -l)
current_line=0

while IFS= read -r line; do
    # Skip comment lines and empty lines
    if [[ "$line" =~ ^#.* ]] || [[ -z "$line" ]]; then
        continue
    fi
    
    # Execute the Redis command
    echo "$line" | redis-cli -p "$PORT" -n "$DB" &>/dev/null
    
    current_line=$((current_line + 1))
    
    # Show progress every 1000 commands
    if [ $((current_line % 1000)) -eq 0 ]; then
        progress=$((current_line * 100 / total_lines))
        echo "Progress: $progress% ($current_line/$total_lines commands)"
    fi
done < "$FILE"

echo "Import completed! Total commands processed: $current_line"
EOF'

# Make the script executable
ssh "${DEST_HOST}" "chmod +x /tmp/import_redis.sh"

# Run the import
echo "Starting import process..."
ssh "${DEST_HOST}" "/tmp/import_redis.sh ${DEST_PORT} ${DEST_DB} /tmp/redis_import.txt"

# Verify the import
echo "Verifying import..."
key_count=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dbsize")
memory_usage=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r'")

echo "Import completed successfully!"
echo "Destination database now contains: ${key_count} keys"
echo "Memory usage: ${memory_usage}"

# Cleanup
echo "Cleaning up temporary files..."
ssh "${DEST_HOST}" "rm -f /tmp/redis_import.txt /tmp/import_redis.sh"

echo "All done!"
