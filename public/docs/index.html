<!DOCTYPE html>
<html>
<head>
  <title>Road name caching</title>
  <meta name="description" content="REST Api">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <link href="assets/bootstrap.min.css" rel="stylesheet" media="screen">
  <link href="assets/prism.css" rel="stylesheet" />
  <link href="assets/main.css" rel="stylesheet" media="screen, print">
  <link href="assets/favicon.ico" rel="icon" type="image/x-icon">
  <link href="assets/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180">
  <link href="assets/favicon-32x32.png" rel="icon" type="image/png" sizes="32x32">
  <link href="assets/favicon-16x16.png"rel="icon" type="image/png" sizes="16x16">
</head>

<body class="container-fluid">

<!-- SIDENAV -->
<script id="template-sidenav" type="text/x-handlebars-template">
<nav id="scrollingNav">
  <div class="sidenav-search">
    <input class="form-control search" data-action='filter-search' type="text" placeholder="{{__ "Filter..."}}">
    <span class="search-reset">x</span>
  </div>
  <ul class="sidenav nav nav-list list">
  {{#each nav}}
    {{#if title}}
      {{#if isHeader}}
        {{#if isFixed}}
          <li class="nav-fixed nav-header navbar-btn nav-list-item" data-group="{{group}}"><a href="#api-{{group}}" data-name="show-api-{{group}}" class="show-api api-{{group}}-init">{{underscoreToSpace title}}</a></li>
        {{else}}
          <li class="nav-header nav-list-item" data-group="{{group}}"><a href="#api-{{group}}" data-group="show-api-{{group}}" class="show-group api-{{group}}-init">{{underscoreToSpace title}}</a></li>
        {{/if}}
      {{else}}
        <li class="{{#if hidden}}hide {{/if}}" data-group="{{group}}" data-name="{{name}}" data-version="{{version}}">
          <a href="#api-{{group}}-{{name}}" title="{{url}}" data-group="show-api-{{group}}" data-name="show-api-{{group}}-{{name}}" class="nav-list-item show-api api-{{group}}-{{name}}-init">{{title}}<div class="nav-list-url-item hide">{{url}}</div></a>
        </li>
      {{/if}}
    {{/if}}
  {{/each}}
  </ul>
</nav>
</script>

<!-- PROJECT -->
<script id="template-project" type="text/x-handlebars-template">
  <div class="pull-left">
    <h1>{{name}}</h1>
    {{#if description}}<h2>{{{nl2br description}}}</h2>{{/if}}
  </div>
  <div class="pull-right">
    {{#if template.withCompare}}
    <div class="btn-group">
      <button id="version" class="btn btn-lg btn-default dropdown-toggle" data-toggle="dropdown">
        <strong>{{version}}</strong>&nbsp;<span class="caret"></span>
      </button>
      <ul id="versions" class="dropdown-menu open-left">
        <li><a id="compareAllWithPredecessor" href="#">{{__ "Compare all with predecessor"}}</a></li>
        <li class="divider"></li>
        <li class="disabled"><a href="#">{{__ "show up to version:"}}</a></li>
      {{#each versions}}
        <li class="version"><a href="#">{{this}}</a></li>
      {{/each}}
      </ul>
    </div>
    {{else}}
    <div id="version" class="well well-sm">
      <strong>{{version}}</strong>
    </div>
    {{/if}}
  </div>
  <div class="clearfix"></div>
</script>

<script id="template-header" type="text/x-handlebars-template">
  {{#if content}}
    <div id="api-_header" class="show-api-article show-api-_-article">{{{content}}}</div>
  {{/if}}
</script>

<script id="template-footer" type="text/x-handlebars-template">
  {{#if content}}
    <div id="api-_footer" class="show-api-article show-api-_-article">{{{content}}}</div>
  {{/if}}
</script>

<script id="template-generator" type="text/x-handlebars-template">
  {{#if template.withGenerator}}
    {{#if generator}}
      <div>
        <p class="text-muted">
          {{__ "Generated with"}} <a href="{{{generator.url}}}">{{{generator.name}}}</a> {{{generator.version}}} - {{{generator.time}}}
        </p>
      </div>
    {{/if}}
  {{/if}}
</script>

<script id="template-sections" type="text/x-handlebars-template">
  <section id="api-{{group}}" class="show-api-group show-api-{{group}}-group {{#if aloneDisplay}} hide{{/if}}">
    <h1 class="color-primary font-weight-bold">{{underscoreToSpace title}}</h1>
    {{#if description}}
      <p>{{{nl2br description}}}</p>
    {{/if}}
    {{#each articles}}
      <div id="api-{{group}}-{{name}}" class="show-api-article show-api-{{group}}-article show-api-{{group}}-{{name}}-article {{#if aloneDisplay}} hide{{/if}}">
        {{{article}}}
      </div>
    {{/each}}
  </section>
</script>

<script id="template-article" type="text/x-handlebars-template">
  <article id="api-{{article.group}}-{{article.name}}-{{article.version}}" {{#if hidden}}class="hide"{{/if}} data-group="{{article.group}}" data-name="{{article.name}}" data-version="{{article.version}}">
    <div class="pull-left">
      <h1><span class="color-primary">{{underscoreToSpace article.groupTitle}}</span>{{#if article.title}} <span class="text-muted">|</span> {{article.title}}{{/if}}</h1>
    </div>
    {{#if template.withCompare}}
    <div class="pull-right">
      <div class="btn-group">
        <button class="version btn btn-default dropdown-toggle" data-toggle="dropdown">
          <strong>{{article.version}}</strong>&nbsp;<span class="caret"></span>
        </button>
        <ul class="versions dropdown-menu open-left">
          <li class="disabled"><a href="#">{{__ "compare changes to:"}}</a></li>
        {{#each versions}}
          <li class="version"><a href="#">{{this}}</a></li>
        {{/each}}
        </ul>
      </div>
    </div>
    {{/if}}
    <div class="clearfix"></div>

    {{#if article.author}}<h4 class="muted">Authored by: {{article.author}}</h4>{{/if}}

    {{#if article.deprecated}}
      <p class="deprecated"><span>{{__ "DEPRECATED"}}</span>
        {{{markdown article.deprecated.content}}}
      </p>
    {{/if}}

    {{#if article.description}}
      <p>{{{nl2br article.description}}}</p>
    {{/if}}
    <span class="method meth-{{toLowerCase article.type}}">{{article.type}}</span>
    <pre data-type="{{toLowerCase article.type}}"><code class="language-http">{{article.url}}</code></pre>

    {{#if article.permission}}
      <p>
        {{__ "Permission:"}}
        {{#each article.permission}}
          {{name}}
          {{#if title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{title}}" data-content="{{nl2br description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
              <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{/if}}
        {{/each}}
      </p>
    {{/if}}

    {{!-- CODE EXAMPLES IN TABS --}}
    {{#ifCond article.examples.length '>' 0}}
      <ul class="nav nav-tabs nav-tabs-examples" role="tablist">
        {{#each article.examples}}
          <li{{#ifCond @index '==' 0}} class="active"{{/ifCond}}>
            <a href="#examples-{{../id}}-{{@index}}" role="tab" data-toggle="tab">{{title}}</a>
          </li>
        {{/each}}
      </ul>

      <div class="tab-content">
      {{#each article.examples}}
        <div class="tab-pane{{#ifCond @index '==' 0}} active{{/ifCond}}" id="examples-{{../id}}-{{@index}}">
          <pre data-type="{{type}}"><code class="language-{{type}}">{{content}}</code></pre>
        </div>
      {{/each}}
      </div>
    {{/ifCond}}

    {{subTemplate "article-param-block" params=article.header _hasType=_hasTypeInHeaderFields section="header"}}
    {{subTemplate "article-param-block" params=article.parameter _hasType=_hasTypeInParameterFields section="parameter"}}
    {{subTemplate "article-query-block" params=article.query _hasType=_hasTypeInParameterFields section="query"}}
    {{subTemplate "article-body-block" params=article.body _hasType=_hasTypeInParameterFields section="body"}}
    {{subTemplate "article-param-block" params=article.success _hasType=_hasTypeInSuccessFields section="success"}}
    {{subTemplate "article-param-block" params=article.error _col1="Name" _hasType=_hasTypeInErrorFields section="error"}}

    {{subTemplate "article-sample-request" article=article id=id}}
  </article>
</script>

<script id="template-article-query-block" type="text/x-handlebars-template">
  {{#if article.query}}
    <h2>{{__ "Query Parameter(s)"}}</h2>
    <table>
      <thead>
        <tr>
          <th style="width: 30%">{{#if ../_col1}}{{__ ../_col1}}{{else}}{{__ "Field"}}{{/if}}</th>
          {{#unless this.Type compare=null}}
            <th style="width: 10%">{{__ "Type"}}</th>
          {{/unless}}
          <th style="width: {{#if ../_hasType}}60%{{else}}70%{{/if}}">{{__ "Description"}}</th>
        </tr>
      </thead>
      <tbody>
        {{#each params}}
          <tr>
            <td class="code">{{this.field}}&nbsp;&nbsp;
            {{#if optional}}
              <span class="label optional">{{__ "optional"}}</span>
            {{else}}
              {{#if ../template.showRequiredLabels}}
                <span class="label required">{{__ "required"}}</span>
              {{/if}}
            {{/if}}
            </td>
            {{#unless this.Type compare=null}}
              <td class="code">{{this.type}}</td>
            {{/unless}}
            <td>{{{nl2br this.description}}}
            {{#if defaultValue}}<p class="default-value">{{__ "Default value:"}} <code>{{{defaultValue}}}</code></p>{{/if}}
            {{#if size}}<p class="type-size">{{__ "Size range:"}} <code>{{{size}}}</code></p>{{/if}}
            {{#if allowedValues}}<p class="type-size">{{__ "Allowed values:"}}
              {{#each allowedValues}}
                <code>{{{this}}}</code>{{#unless @last}}, {{/unless}}
              {{/each}}
              </p>
            {{/if}}
            </td>
          </tr>
        {{/each}}
      </tbody>
    </table>
  {{/if}}
</script>

<script id="template-article-body-block" type="text/x-handlebars-template">
  {{#if article.body}}
    <h2>{{__ "Request Body"}}</h2>
    <table>
      <thead>
        <tr>
          <th style="width: 30%">{{#if ../_col1}}{{__ ../_col1}}{{else}}{{__ "Field"}}{{/if}}</th>
          {{#unless this.Type compare=null}}
            <th style="width: 10%">{{__ "Type"}}</th>
          {{/unless}}
          <th style="width: {{#if ../_hasType}}60%{{else}}70%{{/if}}">{{__ "Description"}}</th>
        </tr>
      </thead>
      <tbody>
        {{#each params}}
          <tr>
            <td class="code">{{this.field}}&nbsp;&nbsp;
            {{#if optional}}
              <span class="label optional">{{__ "optional"}}</span>
            {{else}}
              {{#if ../template.showRequiredLabels}}
                <span class="label required">{{__ "required"}}</span>
              {{/if}}
            {{/if}}
            </td>
            {{#unless this.Type compare=null}}
              <td class="code">{{this.type}}</td>
            {{/unless}}
            <td>
              {{{nl2br this.description}}}
              {{#if defaultValue}}
                <p class="default-value">{{__ "Default value:"}} <code>{{{defaultValue}}}</code></p>
              {{/if}}
              {{#if size}}
                <p class="type-size">{{__ "Size range:"}} <code>{{{size}}}</code></p>
              {{/if}}
              {{#if allowedValues}}
                <p class="type-size">{{__ "Allowed values:"}}
                  {{#each allowedValues}}
                    <code>{{{this}}}</code>{{#unless @last}}, {{/unless}}
                  {{/each}}
                </p>
              {{/if}}
            </td>
          </tr>
        {{/each}}
      </tbody>
    </table>
  {{/if}}
</script>

<script id="template-article-param-block" type="text/x-handlebars-template">
  {{#if params}}
    {{#each params.fields}}
      <h2>{{__ @key}}</h2>
      <table>
        <thead>
          <tr>
          <th style="width: 30%">{{#if ../_col1}}{{__ ../_col1}}{{else}}{{__ "Field"}}{{/if}}</th>
            {{#if ../_hasType}}<th style="width: 10%">{{__ "Type"}}</th>{{/if}}
            <th style="width: {{#if ../_hasType}}60%{{else}}70%{{/if}}">{{__ "Description"}}</th>
          </tr>
        </thead>
        <tbody>
        {{#each this}}
          <tr>
            <td class="code">{{{splitFill field "." "&nbsp;&nbsp;"}}}
            {{#if optional}}
              <span class="label optional">{{__ "optional"}}</span>
            {{else}}
              {{#if ../template.showRequiredLabels}}
                <span class="label required">{{__ "required"}}</span>
              {{/if}}
            {{/if}}</td>
            {{#if ../../_hasType}}
              <td class="code">
                {{{type}}}
              </td>
            {{/if}}
            <td>
            {{{nl2br description}}}
            {{#if defaultValue}}<p class="default-value">{{__ "Default value:"}} <code>{{{defaultValue}}}</code></p>{{/if}}
            {{#if size}}<p class="type-size">{{__ "Size range:"}} <code>{{{size}}}</code></p>{{/if}}
            {{#if allowedValues}}<p class="type-size">{{__ "Allowed values:"}}
              {{#each allowedValues}}
                <code>{{{this}}}</code>{{#unless @last}}, {{/unless}}
              {{/each}}
              </p>
            {{/if}}
            </td>
          </tr>
        {{/each}}
        </tbody>
      </table>
    {{/each}}
    {{#ifCond params.examples.length '>' 0}}
      <ul class="nav nav-tabs nav-tabs-examples" role="tablist">
      {{#each params.examples}}
        <li{{#ifCond @index '==' 0}} class="active"{{/ifCond}}>
          <a href="#{{../section}}-examples-{{../id}}-{{@index}}" role="tab" data-toggle="tab">{{title}}</a>
        </li>
      {{/each}}
      </ul>

      <div class="tab-content">
      {{#each params.examples}}
        <div class="tab-pane{{#ifCond @index '==' 0}} active{{/ifCond}}" id="{{../section}}-examples-{{../id}}-{{@index}}">
        <pre data-type="{{type}}"><code class="language-{{type}}">{{reformat content type}}</code></pre>
        </div>
      {{/each}}
      </div>
    {{/ifCond}}
  {{/if}}
</script>

<script id="template-article-sample-request" type="text/x-handlebars-template">
  {{#if article.sampleRequest}}
    <div class="well">
      <h3>{{__ "Send a Sample Request"}}</h3>
      <form class="form-horizontal">
        <fieldset>
          <div class="form-group">
            <label class="col-md-3 control-label" for="{{../id}}-sample-request-url">URL</label>
            <div class="input-group">
              <span class="input-group-addon">{{__ "url"}}</span>
              <input id="{{../id}}-sample-request-url" type="url" class="form-control sample-request-url" value="{{article.sampleRequest.0.url}}" />
            </div>
          </div>

      {{#if article.header}}
        {{#if article.header.fields}}
          <h3>{{__ "Headers"}}</h3>
          {{#each article.header.fields}}
            <div class="{{../id}}-sample-request-header-fields">
              {{#each this}}
              <div class="form-group">
                <label class="col-md-3 control-label" for="sample-request-header-field-{{field}}">{{field}}</label>
                <div class="input-group">
                  <span class="input-group-addon">{{{type}}}</span>
                  <input type="text" id="sample-request-header-field-{{field}}"
                    class="form-control sample-request-input"
                    value="{{#if defaultValue}}{{ defaultValue }}{{/if}}"
                    placeholder="{{#if defaultValue}}{{ defaultValue }}{{else}}{{field}}{{/if}}"
                    data-family="header"
                    data-name="{{field}}"
                    data-group="{{@../index}}">
                </div>
              </div>
              {{/each}}
            </div>
          {{/each}}
        {{/if}}
      {{/if}}

      {{#if article.parameter}}
        {{#if article.parameter.fields}}
          <h3>{{__ "Parameters"}}</h3>
          {{#each article.parameter.fields}}
            <div class="col-md-3">
              <select   name="{{../id}}-sample-header-content-type" class="{{../id}}-sample-request-param-select sample-header-content-type sample-header-content-type-switch">
                <option value="auto" selected>ajax-auto</option>
                <option value="json" >json</option>
                <option value="form-data" >form-data</option>
              </select>
            </div>

            <div class="{{../id}}-sample-request-param-body {{../id}}-sample-header-content-type-body hide">
              <div class="form-group">
                <div class="input-group">
                  <div class="input-group-addon">json</div>
                  <textarea id="sample-request-body-json" class="form-control sample-request-body" data-sample-request-body-group="sample-request-param-{{@./index}}" rows="6" style="OVERFLOW: visible" {{#if optional}}data-sample-request-param-optional="true"{{/if}}></textarea>
                </div>
              </div>
            </div>
            <div class="{{../id}}-sample-request-param-fields {{../id}}-sample-header-content-type-fields">
              {{#each this}}
              <div class="form-group">
                <label class="col-md-3 control-label" for="sample-request-param-field-{{field}}">{{field}}</label>
                <div class="input-group">
                  <div class="input-group-addon">{{{type}}}</div>
                  {{#if allowedValues}}
                  <div class="input-group-addon sample-request-select">
                    <select class="form-control" data-name="{{field}}" data-family="query" data-group="{{@../index}}" {{#if optional}}data-optional="true"{{/if}}>
                      <option value="" class="empty">&lt;{{__ "No value"}}&gt;</option>
                      {{#each allowedValues}}
                      <option {{#ifCond ../defaultValue '===' this}} selected {{/ifCond}}value="{{{removeDblQuotes this}}}">{{{removeDblQuotes this}}}</option>
                      {{/each}}
                    </select>
                  </div>
                  <input class="invisible">
                  {{else}}
                  <div class="sample-request-input-{{type}}-container"><div>
                  <input id="sample-request-param-field-{{field}}"
                    class="{{#ifCond type '!==' 'Boolean'}}form-control{{/ifCond}} sample-request-param"
                    type="{{setInputType type}}"
                    value="{{#if defaultValue}}{{ defaultValue }}{{/if}}"
                    placeholder="{{#if defaultValue}}{{ defaultValue }}{{else}}{{field}}{{/if}}"
                    data-name="{{field}}"
                    data-family="query"
                    data-group="{{@../index}}"
                    {{#if optional}}data-optional="true"{{/if}}>
                  </div></div>
                  {{/if}}
                </div>
              </div>
              {{/each}}
            </div>
          {{/each}}
        {{/if}}
      {{/if}}

      {{#if article.query}}
        <h3>{{__ "Query Parameters"}}</h3>
        <div class="{{../id}}-sample-request-param-fields {{../id}}-sample-header-content-type-fields">
          {{#each article.query}}
            <div class="form-group">
              <label class="col-md-3 control-label" for="sample-request-param-field-{{field}}">{{field}}{{#if optional}} ({{__ "optional"}}){{/if}}</label>
              <div class="input-group col-md-6">
                <div class="input-group-addon">{{{type}}}</div>
                {{#if allowedValues}}
                <div class="input-group-addon sample-request-select">
                  <select class="form-control" data-name="{{field}}" data-family="query" data-group="{{@../index}}" {{#if optional}}data-optional="true"{{/if}}>
                    <option value="" class="empty">&lt;{{__ "No value"}}&gt;</option>
                    {{#each allowedValues}}
                    <option {{#ifCond ../defaultValue '===' this}} selected {{/ifCond}}value="{{{removeDblQuotes this}}}">{{{removeDblQuotes this}}}</option>
                    {{/each}}
                  </select>
                </div>
                <input class="invisible">
                {{else}}
                <div class="sample-request-input-{{type}}-container"><div>
                  <input id="sample-request-param-field-{{field}}"
                    class="{{#ifCond type '!==' 'Boolean'}}form-control{{/ifCond}} sample-request-input"
                    type="{{setInputType type}}"
                    value="{{#if defaultValue}}{{ defaultValue }}{{/if}}"
                    placeholder="{{#if defaultValue}}{{ defaultValue }}{{else}}{{field}}{{/if}}"
                    data-name="{{field}}"
                    data-family="query"
                    data-group="{{@../index}}"
                    {{#if optional}}data-optional="true"{{/if}}>
                </div></div>
                {{/if}}
              </div>
            </div>
          {{/each}}
        </div>
      {{/if}}

      {{#if article.body}}
        <h3>{{__ "Body"}}</h3>

        <div class="col-md-3">
          <label for="body-content-type-{{this.id}}">{{__ "Content-Type"}}</label>
          <select id="body-content-type-{{this.id}}" data-id="{{this.id}}" class="sample-request-content-type-switch form-control">
            <option value="body-json" selected>json</option>
            <option value="body-form-data">form-data</option>
          </select>
        </div>

        <div class="col-md-9" id="sample-request-body-json-input-{{this.id}}">
          <div class="form-group">
            <div class="input-group">
              <div class="input-group-addon">json</div>
              <textarea class="form-control sample-request-input" rows="6"
                data-family="body-json"
                data-name={{"body"}}
                data-content-type="json"
                {{#if optional}}data-optional="true"{{/if}}>{{body2json article.body}}</textarea>
            </div>
          </div>
        </div>

        <div hidden class="col-md-9" id="sample-request-body-form-input-{{this.id}}">
          {{#each article.body}}
            <div class="form-group">
              <label class="col-md-3 control-label" for="sample-request-param-field-{{field}}">{{field}}</label>
              <div class="input-group">
                <div class="input-group-addon">{{{type}}}</div>
                {{#if allowedValues}}
                <div class="input-group-addon sample-request-select">
                  <select class="form-control" data-name="{{field}}" data-family="body" data-group="{{@../index}}" {{#if optional}}data-optional="true"{{/if}}>
                    <option value="" class="empty">&lt;{{__ "No value"}}&gt;</option>
                    {{#each allowedValues}}
                    <option {{#ifCond ../defaultValue '===' this}} selected {{/ifCond}}value="{{{removeDblQuotes this}}}">{{{removeDblQuotes this}}}</option>
                    {{/each}}
                  </select>
                </div>
                <input class="invisible">
                {{else}}
                <div class="sample-request-input-{{type}}-container"><div>
                  <input id="sample-request-param-field-{{field}}"
                  class="{{#ifCond type '!==' 'Boolean'}}form-control{{/ifCond}} sample-request-input"
                  type="{{setInputType type}}"
                  value="{{#ifCond type '!==' 'Boolean'}}{{#if defaultValue}}{{ defaultValue }}{{/if}}{{/ifCond}}"
                  {{#if checked}}checked{{/if}}
                  placeholder="{{field}}"
                  data-family="body"
                  data-name="{{field}}"
                  data-content-type="form"
                  {{#if optional}}data-optional="true"{{/if}}>
                  </div></div>
                {{/if}}
              </div>
            </div>
          {{/each}}
        </div>
      {{/if}}

          <div class="form-group">
            <div class="controls pull-right">
              <button class="btn btn-primary bg-primary sample-request-send" data-type="{{article.type}}">{{__ "Send"}}</button>
              <button class="btn btn-danger bg-red sample-request-clear" data-type="{{article.type}}">{{__ "Reset"}}</button>
            </div>
          </div>
          <div class="form-group sample-request-response" hidden>
            <h3>
              {{__ "Response"}}
              <button class="btn btn-default btn-xs pull-right sample-request-clear">X</button>
            </h3>
            <pre data-type="json"><code class="language-json sample-request-response-json"></code></pre>
          </div>
        </fieldset>
      </form>
    </div>
  {{/if}}
</script>

<script id="template-compare-article" type="text/x-handlebars-template">
  <article id="api-{{article.group}}-{{article.name}}-{{article.version}}" {{#if hidden}}class="hide"{{/if}} data-group="{{article.group}}" data-name="{{article.name}}" data-version="{{article.version}}" data-compare-version="{{compare.version}}">
    <div class="pull-left">
      <h1>{{underscoreToSpace article.groupTitle}} | {{{showDiff article.title compare.title}}}</h1>
    </div>

    <div class="pull-right">
      <div class="btn-group">
        <button class="btn btn-success" disabled>
          <strong>{{article.version}}</strong> {{__ "compared to"}}
        </button>
        <button class="version btn btn-danger dropdown-toggle" data-toggle="dropdown">
          <strong>{{compare.version}}</strong>&nbsp;<span class="caret"></span>
        </button>
        <ul class="versions dropdown-menu open-left">
          <li class="disabled"><a href="#">{{__ "compare changes to:"}}</a></li>
          <li class="divider"></li>
        {{#each versions}}
          <li class="version"><a href="#">{{this}}</a></li>
        {{/each}}
        </ul>
      </div>
    </div>
    <div class="clearfix"></div>

    {{#if article.description}}
      <p>{{{showDiff article.description compare.description "nl2br"}}}</p>
    {{else}}
      {{#if compare.description}}
      <p>{{{showDiff "" compare.description "nl2br"}}}</p>
      {{/if}}
    {{/if}}

    <span class="method meth-{{toLowerCase compare.type}}">{{compare.type}}</span>
    <pre data-type="{{toLowerCase article.type}}" class="language-html">{{{showDiff article.url compare.url}}}</pre>

    {{subTemplate "article-compare-permission" article=article compare=compare}}

    <ul class="nav nav-tabs nav-tabs-examples" role="tablist">
    {{#each_compare_title article.examples compare.examples}}
      {{#if typeSame}}
        <li{{#ifCond index '==' 0}} class="active"{{/ifCond}}>
          <a href="#compare-examples-{{../../article.id}}-{{index}}" role="tab" data-toggle="tab">{{{showDiff source.title compare.title}}}</a>
        </li>
      {{/if}}

      {{#if typeIns}}
        <li{{#ifCond index '==' 0}} class="active"{{/ifCond}}>
          <a href="#compare-examples-{{../../article.id}}-{{index}}"><ins>{{{source.title}}}</ins></a>
        </li>
      {{/if}}

      {{#if typeDel}}
        <li{{#ifCond index '==' 0}} class="active"{{/ifCond}}>
          <a href="#compare-examples-{{../../article.id}}-{{index}}"><del>{{{compare.title}}}</del></a>
        </li>
      {{/if}}
    {{/each_compare_title}}
    </ul>

    <div class="tab-content">
    {{#each_compare_title article.examples compare.examples}}

      {{#if typeSame}}
        <div class="tab-pane{{#ifCond index '==' 0}} active{{/ifCond}}" id="compare-examples-{{../../article.id}}-{{index}}">
          <pre data-type="{{source.type}}" class="language-{{source.type}}">{{{showDiff source.content compare.content}}}</pre>
        </div>
      {{/if}}

      {{#if typeIns}}
        <div class="tab-pane{{#ifCond index '==' 0}} active{{/ifCond}}" id="compare-examples-{{../../article.id}}-{{index}}">
          <pre data-type="{{source.type}}" class="language-{{source.type}}">{{{source.content}}}</pre>
        </div>
      {{/if}}

      {{#if typeDel}}
        <div class="tab-pane{{#ifCond index '==' 0}} active{{/ifCond}}" id="compare-examples-{{../../article.id}}-{{index}}">
          <pre data-type="{{compare.type}}" class="language-{{source.type}}">{{{compare.content}}}</pre>
        </div>
      {{/if}}

    {{/each_compare_title}}
    </div>

    {{subTemplate "article-compare-param-block" source=article.parameter compare=compare.parameter _hasType=_hasTypeInParameterFields section="parameter"}}
    {{subTemplate "article-compare-param-block" source=article.success compare=compare.success _hasType=_hasTypeInSuccessFields section="success"}}
    {{subTemplate "article-compare-param-block" source=article.error compare=compare.error _col1="Name" _hasType=_hasTypeInErrorFields section="error"}}

    {{subTemplate "article-sample-request" article=article id=id}}

  </article>
</script>

<script id="template-article-compare-permission" type="text/x-handlebars-template">
  <p>
  {{__ "Permission:"}}
  {{#each_compare_list_field article.permission compare.permission field="name"}}
    {{#if source}}
      {{#if typeSame}}
        {{source.name}}
        {{#if source.title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{source.title}}" data-content="{{nl2br source.description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
            <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{#unless _last}}, {{/unless}}
        {{/if}}
      {{/if}}

      {{#if typeIns}}
        <ins>{{source.name}}</ins>
        {{#if source.title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{source.title}}" data-content="{{nl2br source.description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
            <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{#unless _last}}, {{/unless}}
        {{/if}}
      {{/if}}

      {{#if typeDel}}
        <del>{{source.name}}</del>
        {{#if source.title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{source.title}}" data-content="{{nl2br source.description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
            <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{#unless _last}}, {{/unless}}
        {{/if}}
      {{/if}}
    {{else}}
      {{#if typeSame}}
        {{compare.name}}
        {{#if compare.title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{compare.title}}" data-content="{{nl2br compare.description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
            <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{#unless _last}}, {{/unless}}
        {{/if}}
      {{/if}}

      {{#if typeIns}}
        <ins>{{compare.name}}</ins>
        {{#if compare.title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{compare.title}}" data-content="{{nl2br compare.description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
            <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{#unless _last}}, {{/unless}}
        {{/if}}
      {{/if}}

      {{#if typeDel}}
        <del>{{compare.name}}</del>
        {{#if compare.title}}
          <button type="button" class="btn btn-info btn-xs" data-title="{{compare.title}}" data-content="{{nl2br compare.description}}" data-html="true" data-toggle="popover" data-placement="right" data-trigger="hover">
            <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          </button>
          {{#unless _last}}, {{/unless}}
        {{/if}}
      {{/if}}
    {{/if}}
  {{/each_compare_list_field}}
  </p>
</script>

<script id="template-article-compare-param-block" type="text/x-handlebars-template">
  {{#if source}}
    {{#each_compare_keys source.fields compare.fields}}
      {{#if typeSame}}
        <h2>{{__ source.key}}</h2>
        <table>
        <thead>
          <tr>
            <th style="width: 30%">{{#if ../_col1}}{{__ ../_col1}}{{else}}{{__ "Field"}}{{/if}}</th>
            {{#if ../_hasType}}<th style="width: 10%">{{__ "Type"}}</th>{{/if}}
            <th style="width: {{#if ../_hasType}}60%{{else}}70%{{/if}}">{{__ "Description"}}</th>
          </tr>
        </thead>
        {{subTemplate "article-compare-param-block-body" source=source.value compare=compare.value _hasType=../_hasType}}
        </table>
      {{/if}}

      {{#if typeIns}}
        <h2><ins>{{__ source.key}}</ins></h2>
        <table class="ins">
        <thead>
          <tr>
            <th style="width: 30%">{{#if ../_col1}}{{__ ../_col1}}{{else}}{{__ "Field"}}{{/if}}</th>
            {{#if ../_hasType}}<th style="width: 10%">{{__ "Type"}}</th>{{/if}}
            <th style="width: {{#if ../_hasType}}60%{{else}}70%{{/if}}">{{__ "Description"}}</th>
          </tr>
        </thead>
        {{subTemplate "article-compare-param-block-body" source=source.value compare=source.value _hasType=../_hasType}}
        </table>
      {{/if}}

      {{#if typeDel}}
        <h2><del>{{__ compare.key}}</del></h2>
        <table class="del">
        <thead>
          <tr>
            <th style="width: 30%">{{#if ../_col1}}{{__ ../_col1}}{{else}}{{__ "Field"}}{{/if}}</th>
            {{#if ../_hasType}}<th style="width: 10%">{{__ "Type"}}</th>{{/if}}
            <th style="width: {{#if ../_hasType}}60%{{else}}70%{{/if}}">{{__ "Description"}}</th>
          </tr>
        </thead>
        {{subTemplate "article-compare-param-block-body" source=compare.value compare=compare.value _hasType=../_hasType}}
        </table>
      {{/if}}
    {{/each_compare_keys}}

    {{#if source.examples}}
    <ul class="nav nav-tabs nav-tabs-examples" role="tablist">
    {{#each_compare_title source.examples compare.examples}}
      {{#if typeSame}}
        <li{{#ifCond index '==' 0}} class="active"{{/ifCond}}>
          <a href="#{{../../section}}-compare-examples-{{../../article.id}}-{{index}}" role="tab" data-toggle="tab">{{{showDiff source.title compare.title}}}</a>
        </li>
      {{/if}}

      {{#if typeIns}}
        <li{{#ifCond index '==' 0}} class="active"{{/ifCond}}>
          <a href="#{{../../section}}-compare-examples-{{../../article.id}}-{{index}}"><ins>{{{source.title}}}</ins></a>
        </li>
      {{/if}}

      {{#if typeDel}}
        <li{{#ifCond index '==' 0}} class="active"{{/ifCond}}>
          <a href="#{{../../section}}-compare-examples-{{../../article.id}}-{{index}}"><del>{{{compare.title}}}</del></a>
        </li>
      {{/if}}
    {{/each_compare_title}}
    </ul>

    <div class="tab-content">
    {{#each_compare_title source.examples compare.examples}}

      {{#if typeSame}}
        <div class="tab-pane{{#ifCond index '==' 0}} active{{/ifCond}}" id="{{../../section}}-compare-examples-{{../../article.id}}-{{index}}">
          <pre data-type="{{source.type}}" class="language-{{source.type}}">{{{showDiff source.content compare.content}}}</pre>
        </div>
      {{/if}}

      {{#if typeIns}}
        <div class="tab-pane{{#ifCond index '==' 0}} active{{/ifCond}}" id="{{../../section}}-compare-examples-{{../../article.id}}-{{index}}">
          <pre data-type="{{source.type}}"><code class="language-{{source.type}}">{{{source.content}}}</code></pre>
        </div>
      {{/if}}

      {{#if typeDel}}
        <div class="tab-pane{{#ifCond index '==' 0}} active{{/ifCond}}" id="{{../../section}}-compare-examples-{{../../article.id}}-{{index}}">
          <pre data-type="{{compare.type}}"><code class="language-{{source.type}}">{{{compare.content}}}</code></pre>
        </div>
      {{/if}}
    {{/each_compare_title}}
    </div>
    {{/if}}
  {{/if}}
</script>

<script id="template-article-compare-param-block-body" type="text/x-handlebars-template">
  <tbody>
    {{#each_compare_field source compare}}
      {{#if typeSame}}
        <tr>
          <td class="code">
            {{{splitFill source.field "." "&nbsp;&nbsp;"}}}
            {{#if source.optional}}
              {{#if compare.optional}} <span class="label label-optional">{{__ "optional"}}</span>
              {{else}} <span class="label label-optional label-ins">{{__ "optional"}}</span>
              {{/if}}
            {{else}}
              {{#if compare.optional}} <span class="label label-optional label-del">{{__ "optional"}}</span>{{/if}}
            {{/if}}
          </td>

        {{#if source.type}}
          {{#if compare.type}}
          <td>{{{showDiff source.type compare.type}}}</td>
          {{else}}
          <td>{{{source.type}}}</td>
          {{/if}}
        {{else}}
          {{#if compare.type}}
          <td>{{{compare.type}}}</td>
          {{else}}
            {{#if ../../../../_hasType}}<td></td>{{/if}}
          {{/if}}
        {{/if}}
          <td>
            {{{showDiff source.description compare.description "nl2br"}}}
            {{#if source.defaultValue}}<p class="default-value">{{__ "Default value:"}} <code>{{{showDiff source.defaultValue compare.defaultValue}}}</code><p>{{/if}}
          </td>
        </tr>
      {{/if}}

      {{#if typeIns}}
        <tr class="ins">
          <td class="code">
            {{{splitFill source.field "." "&nbsp;&nbsp;"}}}
            {{#if source.optional}} <span class="label label-optional label-ins">{{__ "optional"}}</span>{{/if}}
          </td>

        {{#if source.type}}
          <td>{{{source.type}}}</td>
        {{else}}
          {{{typRowTd}}}
        {{/if}}

          <td>
            {{{nl2br source.description}}}
            {{#if source.defaultValue}}<p class="default-value">{{__ "Default value:"}} <code>{{{source.defaultValue}}}</code><p>{{/if}}
          </td>
        </tr>
      {{/if}}

      {{#if typeDel}}
        <tr class="del">
          <td class="code">
            {{{splitFill compare.field "." "&nbsp;&nbsp;"}}}
            {{#if compare.optional}} <span class="label label-optional label-del">{{__ "optional"}}</span>{{/if}}
          </td>

        {{#if compare.type}}
          <td>{{{compare.type}}}</td>
        {{else}}
          {{{typRowTd}}}
        {{/if}}

          <td>
            {{{nl2br compare.description}}}
            {{#if compare.defaultValue}}<p class="default-value">{{__ "Default value:"}} <code>{{{compare.defaultValue}}}</code><p>{{/if}}
          </td>
        </tr>
      {{/if}}

    {{/each_compare_field}}
  </tbody>
</script>

<div class="container-fluid">
  <div class="row">
    <div id="sidenav" class="span2"></div>
    <div id="content">
      <div id="project"></div>
      <div id="header"></div>
      <div id="sections"></div>
      <div id="footer"></div>
      <div id="generator"></div>
    </div>
  </div>
</div>

<div id="loader">
  <div class="spinner">
    <div class="spinner-container container1">
      <div class="circle1"></div><div class="circle2"></div><div class="circle3"></div><div class="circle4"></div>
    </div>
    <div class="spinner-container container2">
      <div class="circle1"></div><div class="circle2"></div><div class="circle3"></div><div class="circle4"></div>
    </div>
    <div class="spinner-container container3">
      <div class="circle1"></div><div class="circle2"></div><div class="circle3"></div><div class="circle4"></div>
    </div>
    <p>Loading...</p>
  </div>
</div>

<script src="assets/main.bundle.js"></script>
</body>
</html>
