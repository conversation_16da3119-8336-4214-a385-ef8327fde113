# Redis Migration Script

## Overview

The `migrate-redis.sh` script is a comprehensive tool for migrating all Redis data from a source Redis server (`cdas_prod_ets_redis_0`) to a destination Redis server (`ctr-road-name`). The script handles all data types, preserves TTLs, and includes robust error handling and verification.

## Features

### Core Functionality
- **Complete Data Migration**: Migrates all keys, values, and data types (strings, lists, sets, sorted sets, hashes)
- **TTL Preservation**: Maintains expiration times for keys
- **Data Integrity**: Verifies migration success through key count and sample value checks
- **Progress Tracking**: Real-time progress feedback during migration

### Safety Features
- **Pre-migration Validation**: Tests all connections before starting
- **Automatic Backups**: Creates backups of destination database before migration
- **Dry Run Mode**: Test all connections and validations without performing migration
- **Error Handling**: Comprehensive error checking with graceful failure handling
- **Rollback Support**: Backup files can be used to restore previous state

### Technical Features
- **Batch Processing**: Processes data in batches for better performance
- **Multiple Dump Formats**: Creates both Redis command dumps and JSON backups
- **Detailed Logging**: Comprehensive logging of all operations
- **Flexible Configuration**: Command-line options for all parameters

## Prerequisites

1. **SSH Access**: SSH key authentication to both servers (`cdas_prod_ets_redis_0` and `ctr-road-name`)
2. **SSH Configuration**: Both servers should be configured in `~/.ssh/config` with their respective aliases
3. **Redis CLI**: `redis-cli` command available on both remote servers
4. **Node.js**: Required for redis-dump utility (local machine)

## Usage

### Basic Usage
```bash
# Default migration (cdas_prod_ets_redis_0 → ctr-road-name:6379)
./migrate-redis.sh

# Dry run to test connections
./migrate-redis.sh --dry-run
```

### Advanced Usage
```bash
# Custom destination
./migrate-redis.sh -d ************* -p 6380

# Migrate specific databases
./migrate-redis.sh --source-db 1 --dest-db 2

# Custom backup directory
./migrate-redis.sh --backup-dir /path/to/backups
```

### Command Line Options
- `-s, --source-server`: Source server hostname
- `-d, --dest-host`: Destination server hostname/alias
- `-p, --dest-port`: Destination Redis port
- `--source-port`: Source Redis port
- `--source-db`: Source Redis database number
- `--dest-db`: Destination Redis database number
- `--backup-dir`: Backup directory location
- `--dry-run`: Test mode without actual migration
- `-h, --help`: Show help message

## Migration Process

1. **Dependency Check**: Verifies required tools are available
2. **SSH Connection Testing**: Tests SSH connections to both source and destination servers
3. **Redis Connection Testing**: Tests Redis connections on both servers via SSH
4. **Information Gathering**: Collects database statistics from both servers
5. **Backup Creation**: Backs up destination database via SSH
6. **Data Dumping**: Exports all data from source server via SSH
7. **Validation**: Validates dump file integrity
8. **Data Import**: Imports data to destination server via SSH in batches
9. **Verification**: Verifies migration success by comparing both servers
10. **Report Generation**: Creates detailed migration report

## Output Files

All files are stored in the backup directory (default: `./redis-migration-backup/`):

- `redis_dump_TIMESTAMP.txt`: Redis commands dump
- `redis_dump_TIMESTAMP.json`: JSON format backup
- `destination_backup_TIMESTAMP.txt`: Pre-migration destination backup
- `migration_TIMESTAMP.log`: Detailed operation log
- `migration_report_TIMESTAMP.txt`: Migration summary report

## Error Handling

The script includes comprehensive error handling:
- Connection failures are detected early
- Invalid dump files are rejected
- Import errors stop the process immediately
- All operations are logged for troubleshooting

## Recovery

If migration fails or needs to be reversed:
1. Use destination backup file to restore previous state
2. Check log files for error details
3. Re-run with `--dry-run` to test fixes

## Security Considerations

- Uses SSH key authentication (no passwords in script)
- Creates local backups before any destructive operations
- Validates all inputs and connections
- Logs all operations for audit trail

## Performance

- Batch processing for large datasets
- Progress tracking for long operations
- Efficient memory usage with streaming
- Parallel processing where possible

## Troubleshooting

### Common Issues
1. **SSH Connection Failed**: Ensure SSH keys are properly configured
2. **Redis Connection Failed**: Check Redis server status and network connectivity
3. **Permission Denied**: Ensure script has execute permissions (`chmod +x migrate-redis.sh`)
4. **Dump File Empty**: Check source Redis has data and redis-dump is working

### Debug Mode
Add `set -x` to the script for detailed execution tracing.

## Support

For issues or questions:
1. Check the migration log file
2. Run with `--dry-run` to test connections
3. Verify all prerequisites are met
4. Check network connectivity to both servers
