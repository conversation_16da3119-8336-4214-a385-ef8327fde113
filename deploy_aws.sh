#!/usr/bin/env bash

# AWS Deployment Script for CTR Road Name Translation
# This script deploys the application to the AWS EC2 instance created by Terraform

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_PATH="build"
REMOTE_PATH="/var/www/ctr-road-name"
REMOTE_USER="ubuntu"
APP_NAME="roadname"

# Default host configuration (can be overridden by command line)
# Set this to your EC2 instance IP for easier repeated deployments
DEFAULT_HOST="*************"  # Example: "*******" or "ec2-instance.amazonaws.com"

# Default SSH key configuration (can be overridden by command line)
# Set this to your SSH key path for easier repeated deployments
DEFAULT_SSH_KEY="~/.ssh/ctr-road-name-key"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --host HOST        EC2 instance public IP or hostname [default: $DEFAULT_HOST]"
    echo "  -k, --key KEY_FILE     Path to SSH private key file [default: $DEFAULT_SSH_KEY]"
    echo "  -e, --env ENV          Environment (dev, uat, aws-production) [default: aws-production]"
    echo "  -p, --port PORT        SSH port [default: 22]"
    echo "  --skip-build          Skip the build process"
    echo "  --skip-deps           Skip dependency installation"
    echo "  --force-restart       Force complete PM2 restart (slower but more reliable)"
    echo "  --help                Show this help message"
    echo ""
    echo "Note: If DEFAULT_HOST and DEFAULT_SSH_KEY are set in the script, --host and --key become optional"
    echo ""
    echo "Examples:"
    echo "  $0                                                    # Uses default host and key"
    echo "  $0 --host ******* --key ~/.ssh/ctr-road-name-key"
    echo "  $0 -h ec2-instance.amazonaws.com -k ~/.ssh/my-key.pem -e aws-production"
    echo "  $0 --key ~/.ssh/ctr-road-name-key                    # Uses DEFAULT_HOST with custom key"
    echo "  $0 --host *******                                   # Uses DEFAULT_SSH_KEY with custom host"
}

# Parse command line arguments
REMOTE_HOST="$DEFAULT_HOST"
SSH_KEY="$DEFAULT_SSH_KEY"
ENVIRONMENT="aws-production"
SSH_PORT="22"
SKIP_BUILD=false
SKIP_DEPS=false
FORCE_RESTART=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        -k|--key)
            SSH_KEY="$2"
            shift 2
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            SSH_PORT="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --force-restart)
            FORCE_RESTART=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$REMOTE_HOST" ]]; then
    log_error "Remote host is required. Set DEFAULT_HOST in script or use --host option."
    show_usage
    exit 1
fi

if [[ -z "$SSH_KEY" ]]; then
    log_error "SSH key file is required. Use --key option or set DEFAULT_SSH_KEY in script."
    show_usage
    exit 1
fi

# Expand tilde in SSH key path
SSH_KEY="${SSH_KEY/#\~/$HOME}"

if [[ ! -f "$SSH_KEY" ]]; then
    log_error "SSH key file not found: $SSH_KEY"
    exit 1
fi

# SSH command template
SSH_CMD="ssh -i $SSH_KEY -p $SSH_PORT -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST"
SCP_CMD="scp -i $SSH_KEY -P $SSH_PORT -o StrictHostKeyChecking=no"

log_info "Starting deployment to $REMOTE_HOST"
log_info "Environment: $ENVIRONMENT"
log_info "SSH Key: $SSH_KEY"

# Function to test SSH connection
test_ssh_connection() {
    log_info "Testing SSH connection..."
    if $SSH_CMD "echo 'SSH connection successful'" > /dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "Failed to establish SSH connection"
        exit 1
    fi
}

# Function to build the application
build_application() {
    if [[ "$SKIP_BUILD" == true ]]; then
        log_warning "Skipping build process"
        return
    fi

    log_info "Building application..."

    # Install dependencies
    log_info "Installing dependencies..."
    yarn install

    # Compile TypeScript
    log_info "Compiling TypeScript..."
    yarn compile

    # Create build directory
    log_info "Creating build package..."
    rm -rf $BUILD_PATH
    mkdir -p $BUILD_PATH

    # Copy necessary files
    cp package.json $BUILD_PATH/
    cp yarn.lock $BUILD_PATH/

    # Use AWS-specific ecosystem config if available, otherwise use default
    if [[ -f "ecosystem.aws.config.js" ]]; then
        log_info "Using AWS-specific PM2 configuration"
        cp ecosystem.aws.config.js $BUILD_PATH/ecosystem.config.js
    else
        cp ecosystem.config.js $BUILD_PATH/
    fi

    cp -r lib $BUILD_PATH/
    cp -r config $BUILD_PATH/
    cp -r public $BUILD_PATH/

    # Copy scripts directory if it exists
    if [[ -d "scripts" ]]; then
        cp -r scripts $BUILD_PATH/
    fi

    # Ensure environment-specific config exists
    if [[ ! -f "config/${ENVIRONMENT}.json" ]]; then
        log_warning "Environment config config/${ENVIRONMENT}.json not found"
        if [[ -f "config/production.json" ]]; then
            log_info "Creating ${ENVIRONMENT}.json from production.json"
            cp config/production.json $BUILD_PATH/config/${ENVIRONMENT}.json
        else
            log_error "No fallback configuration found"
            exit 1
        fi
    else
        log_info "Using environment config: config/${ENVIRONMENT}.json"
    fi

    log_success "Application built successfully"
}

# Function to stop PM2 processes properly
stop_pm2_processes() {
    log_info "Stopping PM2 processes..."

    # Check if PM2 is running and has our app
    if $SSH_CMD "pm2 list | grep -q '$APP_NAME'"; then
        log_info "Found existing PM2 process for $APP_NAME"

        if [[ "$FORCE_RESTART" == true ]]; then
            log_info "Force restart requested - deleting PM2 process"
            $SSH_CMD "pm2 delete $APP_NAME || true"
        else
            log_info "Gracefully stopping PM2 process"
            $SSH_CMD "pm2 stop $APP_NAME || true"
        fi
    else
        log_info "No existing PM2 process found for $APP_NAME"
    fi

    # Clear PM2 logs to avoid confusion
    $SSH_CMD "pm2 flush || true"
}

# Function to deploy files to remote server
deploy_files() {
    log_info "Deploying files to remote server..."

    # Create remote directory if it doesn't exist
    $SSH_CMD "sudo mkdir -p $REMOTE_PATH && sudo chown $REMOTE_USER:$REMOTE_USER $REMOTE_PATH"

    # Stop PM2 processes properly
    stop_pm2_processes

    # Backup current deployment
    log_info "Creating backup of current deployment..."
    $SSH_CMD "cd $REMOTE_PATH && if [ -d lib ]; then [ -d backup ] && chmod -R u+w backup && rm -rf backup; mkdir -p backup && cp -r lib config package.json ecosystem.config.js backup/ 2>/dev/null || true; fi"

    # Transfer files
    log_info "Transferring files..."
    rsync -avz --delete -e "ssh -i $SSH_KEY -p $SSH_PORT -o StrictHostKeyChecking=no" \
        $BUILD_PATH/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

    # Ensure correct permissions
    $SSH_CMD "chown -R $REMOTE_USER:$REMOTE_USER $REMOTE_PATH"

    # Make scripts executable if they exist
    $SSH_CMD "if [ -d $REMOTE_PATH/scripts ]; then chmod +x $REMOTE_PATH/scripts/*.sh; fi"

    log_success "Files deployed successfully"
}

# Function to install dependencies on remote server
install_dependencies() {
    if [[ "$SKIP_DEPS" == true ]]; then
        log_warning "Skipping dependency installation"
        return
    fi

    log_info "Installing dependencies on remote server..."
    $SSH_CMD "cd $REMOTE_PATH && yarn install --production"
    log_success "Dependencies installed successfully"
}

# Function to start the application
start_application() {
    log_info "Starting application with PM2..."
    log_info "Environment: $ENVIRONMENT"

    # Verify configuration file exists
    $SSH_CMD "cd $REMOTE_PATH && if [ ! -f config/${ENVIRONMENT}.json ]; then echo 'ERROR: Configuration file config/${ENVIRONMENT}.json not found'; exit 1; fi"

    # Start the application with proper environment
    if $SSH_CMD "pm2 list | grep -q '$APP_NAME.*online'"; then
        log_info "Application already running, restarting..."
        $SSH_CMD "cd $REMOTE_PATH && NODE_ENV=$ENVIRONMENT pm2 restart ecosystem.config.js --env $ENVIRONMENT"
    else
        log_info "Starting new application instance..."
        $SSH_CMD "cd $REMOTE_PATH && NODE_ENV=$ENVIRONMENT pm2 start ecosystem.config.js --env $ENVIRONMENT"
    fi

    # Wait a moment for startup
    sleep 3

    # Save PM2 configuration
    $SSH_CMD "pm2 save"

    # Show PM2 status
    log_info "PM2 Status:"
    $SSH_CMD "pm2 status"

    log_success "Application started successfully"
}

# Function to verify deployment and check application health
check_health() {
    log_info "Verifying deployment and checking application health..."

    # Wait a moment for the application to start
    sleep 5

    # Verify PM2 status
    log_info "Checking PM2 status..."
    if ! $SSH_CMD "pm2 status $APP_NAME | grep -q 'online'"; then
        log_error "PM2 process is not online"
        $SSH_CMD "pm2 logs $APP_NAME --lines 20"
        return 1
    fi
    log_success "PM2 process is online"

    # Verify configuration is loaded correctly
    log_info "Verifying configuration..."
    if ! $SSH_CMD "cd $REMOTE_PATH && test -f config/${ENVIRONMENT}.json"; then
        log_error "Configuration file config/${ENVIRONMENT}.json not found"
        return 1
    fi
    log_success "Configuration file exists"

    # Check if the application is responding
    log_info "Testing application endpoints..."
    APP_PORT=$($SSH_CMD "cd $REMOTE_PATH && node -e \"try { console.log(require('./config/${ENVIRONMENT}.json').port || 3031); } catch(e) { console.log('3031'); }\"")

    # Test application response with retries
    local retry_count=0
    local max_retries=6
    local success=false

    while [ $retry_count -lt $max_retries ]; do
        if $SSH_CMD "curl -f -s --max-time 10 http://localhost:$APP_PORT/health > /dev/null 2>&1"; then
            log_success "Health endpoint is responding"
            success=true
            break
        elif $SSH_CMD "curl -f -s --max-time 10 http://localhost:$APP_PORT > /dev/null 2>&1"; then
            log_success "Application root endpoint is responding"
            success=true
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                log_warning "Application not responding, retrying ($retry_count/$max_retries)..."
                sleep 5
            fi
        fi
    done

    if [ "$success" = false ]; then
        log_error "Application is not responding after $max_retries attempts"
        log_info "Checking recent logs..."
        $SSH_CMD "pm2 logs $APP_NAME --lines 20"
        return 1
    fi

    # Verify environment is correctly set
    log_info "Verifying environment configuration..."
    if $SSH_CMD "pm2 show $APP_NAME | grep -q 'NODE_ENV.*$ENVIRONMENT'"; then
        log_success "Environment is correctly set to $ENVIRONMENT"
    else
        log_warning "Could not verify environment setting"
    fi

    # Show final status
    log_info "Final PM2 status:"
    $SSH_CMD "pm2 status $APP_NAME"

    log_success "Deployment verification completed successfully"
}

# Function to show deployment summary
show_summary() {
    log_info "Deployment Summary:"
    echo "  Host: $REMOTE_HOST"
    echo "  Environment: $ENVIRONMENT"
    echo "  Remote Path: $REMOTE_PATH"
    echo "  Application: $APP_NAME"
    echo "  Configuration: config/${ENVIRONMENT}.json"
    echo ""

    # Get application URL
    APP_PORT=$($SSH_CMD "cd $REMOTE_PATH && node -e \"try { console.log(require('./config/${ENVIRONMENT}.json').port || 3031); } catch(e) { console.log('3031'); }\"" 2>/dev/null || echo "3031")
    echo "  Application URL: http://$REMOTE_HOST:$APP_PORT"
    echo ""

    log_info "Useful commands:"
    echo "  SSH to server: $SSH_CMD"
    echo "  Check PM2 status: $SSH_CMD 'pm2 status'"
    echo "  View logs: $SSH_CMD 'pm2 logs $APP_NAME'"
    echo "  View recent logs: $SSH_CMD 'pm2 logs $APP_NAME --lines 50'"
    echo "  Restart app: $SSH_CMD 'cd $REMOTE_PATH && pm2 restart $APP_NAME'"
    echo "  Stop app: $SSH_CMD 'pm2 stop $APP_NAME'"
    echo "  Monitor app: $SSH_CMD 'pm2 monit'"
    echo "  Health check: $SSH_CMD '$REMOTE_PATH/scripts/health-check.sh $APP_PORT'"
    echo ""

    log_info "Quick tests:"
    echo "  curl http://$REMOTE_HOST:$APP_PORT"
    echo "  curl http://$REMOTE_HOST:$APP_PORT/health"
}

# Cleanup function
cleanup() {
    if [[ -d "$BUILD_PATH" ]]; then
        log_info "Cleaning up build directory..."
        rm -rf $BUILD_PATH
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment process
main() {
    log_info "=== CTR Road Name Translation Deployment ==="
    log_info "Host: $REMOTE_HOST"
    log_info "Environment: $ENVIRONMENT"
    log_info "Force restart: $FORCE_RESTART"
    echo ""

    # Execute deployment steps with error handling
    if ! test_ssh_connection; then
        log_error "SSH connection test failed"
        exit 1
    fi

    if ! build_application; then
        log_error "Application build failed"
        exit 1
    fi

    if ! deploy_files; then
        log_error "File deployment failed"
        exit 1
    fi

    if ! install_dependencies; then
        log_error "Dependency installation failed"
        exit 1
    fi

    if ! start_application; then
        log_error "Application startup failed"
        exit 1
    fi

    if ! check_health; then
        log_error "Health check failed"
        log_warning "Deployment may have issues. Check the logs above."
        exit 1
    fi

    show_summary

    log_success "Deployment completed successfully!"
    log_info "Application is running and healthy at http://$REMOTE_HOST"
}

# Run main function
main