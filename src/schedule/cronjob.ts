const dump = require('redis-dump');
import moment from 'moment';
import {getConfigVar} from '../helper/helper';
import cron from 'cron';
import S3AWS from '../library/s3AWS';
import Redis from '../library/redis';
import {API_GEO_CODE} from '../helper/helper'

const CronJob = cron.CronJob;
const redis = new Redis();
const s3 = new S3AWS();
const now = Math.round(+new Date()/1000);
const job = new CronJob(
  '0 0 0 * * *',
  //will run every day at 12:00 AM
  // '* * * * * *',
  async function() {
    let scheduler;
    try {
      scheduler = await redis.raceCondition('cronjob-daily', 3000);
      let logApi = await redis.client.lRange(API_GEO_CODE, 0, -1);
      if (logApi.length > 0) {
        let logApiFileName = `apiLogs_${moment(new Date()).format("DD-MM-YYYY")}.txt`;
        await s3.upload(String(logApi),`${moment(new Date()).format("DD-MM-YYYY")}/${logApiFileName}`);
        await redis.client.del(API_GEO_CODE);
      }
      dump({
          // These are default values, you can omit them
          filter: '*',
          port: getConfigVar('redisPort'),
          host: getConfigVar('redisHost')
        },
        async function(err:any, result:any){
          let fileName = `${now}_${moment(new Date()).format("DD-MM-YYYY")}.txt`
          await s3.upload(String(result),`${moment(new Date()).format("DD-MM-YYYY")}/${fileName}`);
        },);
      scheduler && await redis.client.del(scheduler);
    } catch (e: any){
      console.log(e.message);
    }
  },
null,
  true,
  'UTC'
);


export default job;

