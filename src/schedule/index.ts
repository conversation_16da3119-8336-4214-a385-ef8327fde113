import { checkTokenOneMap, reverse_geo_api_name, wait } from '../helper/helper';
import Processor from '../services/road-map/process';
import Redis from '../library/redis';
import Queue from '../helper/queue';
// Don't remove this comment. It's needed to format import lines nicely.

export default class Schedule {
  private readonly scheduler;
  private processor: Processor;
  private readonly redis: Redis;

  constructor() {
    this.scheduler = new Queue('acquisition-request-list', {});
    this.processor = new Processor('acquisition-request-list');
    this.redis = new Redis();
  }

  process() {
    let scheduler;
    try {
      this.scheduler.process(async (data) => {
        scheduler = await this.redis.raceCondition('acquisition-request-list', 60);
        if (!scheduler) return;
        let [errToken, token] = await wait(checkTokenOneMap());
        if (errToken) return;
        let lat = Number(data.lat);
        let lng = Number(data.lng);
        let [errName, name] = await wait(reverse_geo_api_name(lat, lng));
        if (errName) return;
        if (name) await this.processor.write_name(lat, lng, name); //write to cache
        // await this.processor.write_name_to_square(lat, lng, name); //write to cache
        scheduler && await this.redis.client.del(scheduler);
        return;
      });
    } catch (e: any) {
      console.log('process geo api error: ' + e.message);
    }
  }
}
