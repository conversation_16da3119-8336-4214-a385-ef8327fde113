import aws from 'aws-sdk'
import multer from 'multer'
import multerS3 from 'multer-s3'
import path from "path";
import {getConfigVar} from "../helper/helper";
const csv = require('csvto<PERSON>son');

let s3:any = new aws.S3({
  secretAccessKey: getConfigVar('aws').secretKey,
  accessKeyId: getConfigVar('aws').accessKey,
})
const isUpload = (req:any, res:any, next:any) => {
  return multer({
    storage: multerS3({
      s3: s3,
      bucket: getConfigVar('aws').csvBucket,
      contentType: multerS3.AUTO_CONTENT_TYPE,
      metadata: function (req:any, file:any, cb:any) {
        cb(null, {fieldName: file.fieldname});
      },
      key: function (req:any, file:any, cb:any) {
        let ext = path.extname(file.originalname);
        cb(null, `${Date.now().toString()}${ext}`)
      }
    }),
    fileFilter: function (req, file, callback) {
      const mimeType = file.mimetype;
      if (mimeType !== 'text/csv') {
        return callback(null, false);
      }
      callback(null, true);
    },
  }).array('file', 1)(req, res, function (err:any) {
    next();
  });
}

const csvToJSON = async (fileName: string) => {
  // get csv file and create stream
  const stream = s3.getObject({
    Bucket: getConfigVar('aws').csvBucket,
    Key: fileName
  }).createReadStream();
  // convert csv file (stream) to JSON format data
  return csv().fromStream(stream);
};

export {
  isUpload,
  csvToJSON
}

