import * as helper from '../helper/helper';
export default async function authAPI(req:any,res:any, next:any) {
  try {
    let data = {
      method: 'GET',
      url: helper.getConfigVar('urlAuth'),
      token: req.headers.authorization
    }
    let result:any = await helper.request(data);
    if (result.status !== 200) res.status(401).json({message: 'UnAuthorize'});
    req.user = result.data.user;
    next();
  } catch(e) {
    res.status(401).json({message: 'UnAuthorize'});
  }
}
