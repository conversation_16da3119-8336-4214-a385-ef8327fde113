import * as helper from "../helper/helper";
import Redis from "../library/redis";
let redis = new Redis();
export default async function raceLimit(request: any, res: any, next: any) {
  try {
    const action = request.originalUrl;
    const key = await redis.raceCondition(
      `${action}:${request.user?._id || "no_auth"}`,
      60 * 60 * 24 * 365
    );
    
    next();
  } catch (e: any) {
    res.status(500).json({ message: e.message });
  }
}
