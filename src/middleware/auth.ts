import * as helper from '../helper/helper';
import app from '../app';
import * as errors from '@feathersjs/errors';
export default async function auth(context: any) {
  try {
    let data = {
      method: 'GET',
      url: app.get('urlAuth'),
      token: context.params.headers.authorization
    }
    // let result:any = await helper.request(data);
    // if (result.status !== 200) throw new errors.NotAuthenticated();
    // context.params.user = result.data.user;
  } catch(e) {
    throw new errors.NotAuthenticated();
  }
}
