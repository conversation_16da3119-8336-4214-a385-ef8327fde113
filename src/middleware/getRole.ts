import * as helper from '../helper/helper';
import app from '../app';
import * as errors from '@feathersjs/errors';
export default async function auth(authorization: string) {
  try {
    let data = {
      method: 'GET',
      url: `${app.get('CTRUrl')}/`,
      token: authorization
    }
    let result:any = await helper.request(data);
    if (result.status !== 200) throw new errors.NotAuthenticated();
    return result.data;
  } catch(e) {
    throw new errors.NotAuthenticated();
  }
}
