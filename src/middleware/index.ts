import { Application } from '../declarations';
import * as response from '../helper/response';
import * as helper from '../helper/helper';
// Don't remove this comment. It's needed to format import lines nicely.
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function

export default function (app: Application) {
  // console.log(app);
  // return async function auth(req: any,res: any, next: any) {
  //   console.log('vao day');
  //   //try {
  //   let data = {
  //     method: 'GET',
  //     url: configs.ctrUrl,
  //     token: req.headers.authorization
  //   }
  //   let result = await helper.request(data);
  //   if (result.status !== true) return response.error(res, null,401);
  //   req.user = result.data;
  //   next();
  //   // } catch(e) {
  //   //   return response.error(res, e, 401);
  //   // }
  // }
}

