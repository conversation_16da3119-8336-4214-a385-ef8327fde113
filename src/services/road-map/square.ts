import Redis from "../../library/redis";
const redis_square_key_prefix = "s-";

const SQUARE_SIZE = 16;
const CELL_SIZE = 2;
const MAX_CELL_COL = SQUARE_SIZE/CELL_SIZE+1;
const MAX_CELL:number = MAX_CELL_COL*MAX_CELL_COL;
const NAME_DELIMITER = ",";

export default class Square {
  private square_size: number;
  private readonly cell_size: number;
  private readonly square_row: number;
  private readonly square_col: number;
  private readonly lat_s: number;
  private readonly lng_s: number;
  private name_block: any[];
  private readonly key: string;
  private redis: Redis;
  constructor(lat: number, lng: number) {
    this.square_size = SQUARE_SIZE;
    this.cell_size = CELL_SIZE;

    this.square_row = Math.ceil(lat/SQUARE_SIZE);     //row and col number for the square
    this.square_col = Math.ceil(lng/SQUARE_SIZE);

    this.lat_s = this.square_row*SQUARE_SIZE;   //differential values of lat, lng of the top left corner
    this.lng_s = this.square_col*SQUARE_SIZE;

    this.name_block = [];        //hold the data for all road name within the square
    this.key = redis_square_key_prefix + String(this.square_row)  + "-" + String(this.square_col);
    //redis
    this.redis = new Redis()
  }

  //read square info
  async read_sq() {
    //access Redis to retrieve value for the key
    let names = await this.redis.client.get(this.key);
    if(names==null) return null;

    this.name_block = [];   //clear the array first
    //split names for the key to items and push parseInto the array
    this.name_block = names.split(NAME_DELIMITER);
  }


  //read name for cell
  read_cell(lat_d:number, lng_d:number) {
    //compute the cell num
    //let cell_num = (lat_d-this.lat_s)/this.cell_size)*MAX_CELL_COL + ((lng_d-this.lng_s)/this.cell_size);
    let cell_num = ((this.lat_s - lat_d)/this.cell_size)*MAX_CELL_COL + ((this.lng_s - lng_d)/this.cell_size);
    cell_num = Number(Math.floor(cell_num).toFixed(0));
    //check if the lat-lng is inside this square

    if(cell_num >= MAX_CELL) return null;    //out of range :(
    return this.name_block[cell_num];
  }

  // *** IMPORTANT: only the Acquisition Task may call functions below - so that updates are done in a controlled manner

  //update current square info to Db
  async update_sq() {
    //convert this.name_block to string as value for key
    let names = "";

    for(let i=0; i < Number(Math.floor(MAX_CELL).toFixed(0)); i++) {
      names = names + this.name_block[i] + NAME_DELIMITER ;
    }

    //save info to Redis
    await this.redis.client.set(this.key, names);
  }

  //update name for cell
  update_cell(lat_d:number, lng_d:number, name:string) {
    //compute the cell num
    // let cell_num = (lat_d-this.lat_s)/this.cell_size*MAX_CELL_COL + (lng_d-this.lng_s)/this.cell_size;

    let cell_num = ((this.lat_s - lat_d)/this.cell_size)*MAX_CELL_COL + ((this.lng_s - lng_d)/this.cell_size);
    cell_num = Number(Math.floor(cell_num).toFixed(0));

    //check that cell_number is within range
    if(cell_num >= MAX_CELL) return;    //out of range :(

    this.name_block[cell_num] = name;
  }

  // //delete square info from Db
  // delete_sq(lat, lng) {
  //   //access Redis to delete the key-value pair
  //   redis_deleete(this.key);
  // }

}

