// Initializes the `road-map` service on path `/road-map`
import { ServiceAddons } from "@feathersjs/feathers";
import { Application } from "../../declarations";
import { RoadMap } from "./road-map.class";
import createModel from "../../models/road-map.model";
import hooks from "./road-map.hooks";
import validate from "../../helper/validate";
import schema from "../../validation/road-name";
import Processor from "./process";
import authAPI from "../../middleware/apiAuth";
import { isUpload, csvToJSON } from "../../middleware/isUploadToS3";
import raceLimit from "../../middleware/raceCondition";

// Add this service to the service type index
declare module "../../declarations" {
  interface ServiceTypes {
    "road-map": RoadMap & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get("paginate"),
  };

  // Initialize our service with any options it requires
  app.use("/road-map", new RoadMap(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service("road-map");

  /**
   * @api {post} /road-map/init-db init road name via csv
   * @apiHeader {String} Authorization token of user
   * @apiName Road name Init Data By CSV
   * @apiGroup RoadName
   * @apiParam {file} file csv file .
   *
   * @apiSuccess {Object} status.
   */
  app.post(
    "/road-map/init-db",
    [authAPI, isUpload],
    async (req: any, res: any) => {
      try {
        let data = await csvToJSON(req.files[0].key);
        let processor = new Processor("acquisition-request-list");
        for (const item of data) {
          const name = await processor.read_name(item.lat, item.lng);
          console.log(
            "📖 ~ file: road-map.service.ts ~ line 48 ~ app.post ~ name",
            name
          );
          await new Promise((resolve) => setTimeout(resolve, app.get("delay")));
        }
        res.json({
          message: "Init Data to queue redis success",
        });
      } catch (err: any) {
        const { message } = err;
        res.status(500).json({ message });
      }
    }
  );

  /**
   * @api {post} /road-map/name get road name by lat long
   * @apiHeader {String} Authorization token of user
   * @apiName Road name get by companyId
   * @apiGroup RoadName
   * @apiParam {String} latitude of trailer .
   * @apiParam {String} longitude of trailer .
   *
   * @apiSuccess {Object} road name object.
   */
  app.post("/road-map/name", authAPI, async (req: any, res: any) => {
    try {
      await validate(schema.get, req.body);
      console.log('request-from', req.headers["request-from"]);
      let { latitude, longitude } = req.body;
      let processor = new Processor("acquisition-request-list");
      let name = await processor.read_name(latitude, longitude);
      let cache = "Redis";
      if (!name) {
        cache = "not available. Send to Queue in Redis";
        name = "NOT_FOUND";
      }
      res.json({
        name: name,
        cache: cache,
      });
    } catch (err: any) {
      const { message } = err;
      res.status(500).json({ message });
    }
  });

  service.hooks(hooks);
}
