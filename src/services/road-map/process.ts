import Redis from '../../library/redis';
import { reverse_geo_api_name, checkTokenOneMap } from '../../helper/helper';
import Square from './square';
import Queue from '../../helper/queue';

let cacheLocationName: any = {};

let management: { [key: string]: Queue } = {};
export default class Processor {
  private queue;
  private redis: Redis | undefined;

  constructor(name: string) {
    this.redis = new Redis();
    if (management[name]) {
      this.queue = management[name];
      return this;
    }
    this.queue = new Queue(name);
    management[name] = this.queue;
  }

  generateRedisKey(lat: number, lng: number) {
    const latStr = Math.round(lat * 100_000);
    const lngStr = Math.round(lng * 100_000);
    return `c-${latStr}-${lngStr}`;
  }

  //read from redis Db
  async read_name(lat: number, lng: number) {
    //truncat the lat-lng to round off at ~20m resolution, e.g. lat20-lng20
    try {
      // let lat20 = (lat * 100000 / 20) * 20;
      // let lng20 = (lng * 100000 / 20) * 20;

      //compose the keys c-lat20-lng20, save name in Redis
      // let key = 'c-' + String(lat20) + '-' + String(lng20);
      let key = this.generateRedisKey(lat, lng);
      let name = await this.redis?.client.get(key);

      console.log('name readname', name);
      if (name != null) {
        cacheLocationName[key] = name;
        setTimeout(() => {
          delete cacheLocationName[key];
        }, 1000 * 5 * 60);
        return name;
      }     //cache hit:)

      //if not found , write a new item to the request list
      //this list will be capped at max 1000 request
      // key = "acquisition-request-list";
      // await redisClient.rPush(key, JSON.stringify({lat,lng}));
      await this.queue.add({ lat, lng });
    } catch (e) {
      console.log('read_name error: ', e);
    }
    return null;
  }

  async write_name(lat: number, lng: number, name: string) {
    //compose the keys c-lat20-lng20, save name in Redis
    let key = this.generateRedisKey(lat, lng);
    //save data to redis
    await this.redis?.client.set(key, name);
    const threeMonthsInSeconds = 3 * 30 * 24 * 60 * 60;
    await this.redis?.client.setEx(key, threeMonthsInSeconds, name);
  }

  //this is the task that will serve the request to find road name for new locations
  // it will update the location into Redis, so the next time the result may be found
  // recommend to use agenda in node nodejs to trigger it - https://github.com/agenda/agenda
  // should limit to 240 requests/minute
  async acquisition_task(max_req: number) {
    for (let i = 0; i < max_req; i++) {

      //pop the last request from Redis acquisition-request-list
      let key = 'acquisition-request-list';
      let request = await this.redis?.client.lRange(key, 0, -1);
      if (request.length < 1) break;     //request list is empty
      //call reverse-geo API to get name based on lat-lng
      await checkTokenOneMap();
      await Promise.all(
        request.map(async (item: any) => {
          item = JSON.parse(item);
          let lat = Number(item.lat);
          let lng = Number(item.lng);
          let name = await reverse_geo_api_name(lat, lng);
          await this.write_name(lat, lng, name); //write to cache
        }),
      );
      //delete list
      await this.redis?.client.del(key);
    }
  }

  //
  // *** below methods support scenario when square is used to aggregate cells ***
  //

  async read_name_from_square(lat: number, lng: number) {
    //create a square object using lat-lng
    let sq = new Square(lat, lng);

    //read the info for the square from Redis
    let status = await sq.read_sq();

    //read name for the cell
    let name = sq.read_cell(lat, lng);

    if (name != null) return name;     //cache hit:)

    //if not found , write a new item to the request list
    //this list will be capped at max 1000 request
    // let key = "acquisition-request-list";
    // await redisClient.rPush(key, JSON.stringify({lat,lng}));
    await this.queue.add({ lat, lng });

    return null;
  }

  //to acquire name for point that is not cached
  async write_name_to_square(lat: number, lng: number, name: string) {
    //create a square object using lat-lng
    let sq = new Square(lat, lng);

    //read the info for the square from Redis
    let status = await sq.read_sq();

    //update info into square object
    sq.update_cell(lat, lng, name);

    //save data into Redis
    await sq.update_sq();

    //log the new name acquired in daily log file
    //log_name(lat, lng, name, timestamp);
  }

}
