import aws from "aws-sdk"
import {getConfigVar} from '../helper/helper';

let s3 = new aws.S3(getConfigVar('awsOptions'))

export default class S3AWS {
  private s3;
  constructor() {
    this.s3 = s3
  }

  public async upload(localData:any , fileName:string) {
    this.s3.putObject({
      Bucket: getConfigVar('aws').bucketName,
      Body: localData,
      ACL:'public-read',
      Key: fileName
    })
    .promise()
    .then(res => {
      console.log(`Upload succeeded - `, res);
    })
    .catch(err => {
      console.log("Upload failed:", err);
    });
  }
}



