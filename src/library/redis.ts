import * as redis from "redis";

let redisClient: any;
let redisUrl: string = '';

export default class Redis {
  client;
  constructor() {
    if (redisClient) {
      this.client = redisClient;
      return this;
    }
    this.client = redis.createClient({
      url: redisUrl,
    });
    redisClient = this.client;
    this.client.on("success", function () {
      console.log(`Connect Success To Redis`);
    });

    this.client.on("error", function (err: any) {
      console.log("redis", err);
    });
    this.client
      .connect()
      .then(() => {})
      .catch((e: any) => {
        console.log(e);
      });
  }

  async raceCondition(type: string, expire = 60) {
    const key = "RACE_CONDITION:" + type;
    // increase the key value by 1
    // console.log("LOG-key", key);
    const isKeySet = await this.client.incr(key);
    if (isKeySet > 1) return null;
    await this.client.expire(key, expire || 60);
    return key;
  }

  async rateLimit(params: {key: string, limit: number, expire: number}) {
    const { key, limit, expire } = params
    const redisKey = "RATE_LIMIT:" + key;
    const value = await this.client.incr(redisKey);
    if (value > limit) {
      return false;
    }
    if (value === 1) {
      await this.client.expire(key, expire);
    }
    return true

  }

}


export function setRedisUrl(url: string) {
  redisUrl = url;
}
