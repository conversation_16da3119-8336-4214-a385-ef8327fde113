import Redis from '../library/redis';
import moment from 'moment';

export type QueueOptions = {
  delay?: number;
  limit?: number;
  time_unit?: moment.unitOfTime.Base;
}

export default class Queue {
  redis: Redis;
  name: string;
  options: QueueOptions;

  constructor(name: string, options: QueueOptions = {}) {
    this.redis = new Redis();
    this.name = name;
    this.options = {
      delay: 1000,
      ...options,
    };
    if (this.options.limit && !this.options.time_unit) {
      this.options.time_unit = 'seconds';
    }
  }

  async add(data: any) {
    let stringifyData;
    try {
      stringifyData = JSON.stringify(data);
    } catch (error) {
      throw new Error('Data must be a valid JSON object');
    }
    await this.redis.client.rPush(this.name, stringifyData);
  }

  async process(callback: (data: any) => Promise<void>) {
    while (true) {
      let data = await this.redis.client.lPop(this.name);
      if (!data) {
        await new Promise((resolve) => setTimeout(resolve, this.options.delay));
        continue;
      }
      if (this.options.limit) {
        // @ts-ignore
        const time = moment().startOf(this.options.time_unit).format('YYMMDDHHmmss');
        const canProcess = await this.redis.rateLimit({
          key: `${this.name}_${time}`,
          limit: this.options.limit,
          // @ts-ignore
          expire: 1 + moment().endOf(this.options.time_unit).diff(moment().startOf(this.options.time_unit), 'seconds'),
        });
        if (!canProcess) {
          await new Promise((resolve) => setTimeout(resolve, this.options.delay));
          continue;
        }
      }
      console.log("☘ [Queue] Processing", data);
      await callback(JSON.parse(data));
    }
  }
}
