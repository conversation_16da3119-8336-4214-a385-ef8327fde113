
const error = (res:any, err:any, code:any) => {
  let msgErr = err;
  if (typeof err === 'object' && typeof err.message !== 'undefined') {
    msgErr = err.message;
  }
  if (typeof code !== 'undefined') res.statusCode = code;
  return res.json({ success: false, message: msgErr });
};

const success = (res:any, data:any, code:any) => {
  if (typeof code !== 'undefined') res.statusCode = code;
  return res.json({
    success: true,
    data,
  });
};

export {
  error,
  success
};
