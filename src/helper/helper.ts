import axios from 'axios';
import {to} from 'await-to-js';

const feathers = require("@feathersjs/feathers");
const configuration = require("@feathersjs/configuration");
// Use the application root and `config/` as the configuration folder
const app = feathers().configure(configuration());
import Redis from "../library/redis";
const API_GEO_CODE = "apiGeoCode";

const request = async (data: any) => {
  /* ... */
  try {
    let options = {
      method: data.method,
      url: data.url,
      headers: {
        Authorization: data.token || null,
        "content-type": "application/json",
        "cache-control": "no-cache",
      },
      data: data.body,
    };
    return await axios(options);
  } catch (e: any) {
    console.log("axios error ", e.message);
    if (e.response) {
      console.log("axios error response ", e.response.data);
    }
  }
};

const wait = async (_promise: any) => {
  let err: any;
  let res: any;
  [err, res] = await to(_promise);
  if (err) return [err];
  return [null, res];
};

const checkTokenOneMap = async () => {
  let redis = new Redis();
  try {
    let oneMapToken: any = await redis.client.get("oneMapToken");
    let isExpired = false;
    if (oneMapToken) {
      let now = Math.round(+new Date() / 1000);
      let expiryTimestamp = JSON.parse(oneMapToken).expiry_timestamp;
      if (Number(expiryTimestamp) < Number(now)) isExpired = true;
    }
    if (!oneMapToken || isExpired) {
      let dataCondition = {
        method: "POST",
        // url: `${app.get('oneMap').url}/privateapi/auth/post/getToken`,
        url: `${app.get("oneMap").url}/api/auth/post/getToken`,
        body: {
          email: app.get("oneMap").email,
          password: app.get("oneMap").password,
        },
      };
      let result: any = await request(dataCondition);
      await redis.client.set("oneMapToken", JSON.stringify(result.data));
    }
  } catch (e: any) {
    console.log("checkTokenOneMap Error: ", e.message);
  }
};

const reverse_geo_api_name = async (latitude: number, longitude: number) => {
  let redis = new Redis();
  try {
    let oneMapToken: any = await redis.client.get("oneMapToken");
    let stringLatLong = `${latitude.toString()},${longitude.toString()}`;
    let data = {
      method: "GET",
      token: `Bearer ${JSON.parse(oneMapToken).access_token}`,
      url: `${
        app.get("oneMap").url
      }/api/public/revgeocode?location=${encodeURIComponent(
        stringLatLong
      )}&buffer=50&addressType=All&otherFeatures=N`,
    };
    let result: any = await request(data);
    if (result?.status === 200) {
      await redis.client.rPush(API_GEO_CODE, data.url);
      if (result.data.GeocodeInfo?.length > 0)
        return result.data.GeocodeInfo[0].ROAD;
      return false;
    }
    return false;
  } catch (e) {
    console.log("reverse_geo_api_name Error: ", e);
  }
};

const getConfigVar = (name: string) => {
  return app.get(name);
};


export {
  request,
  wait,
  reverse_geo_api_name,
  checkTokenOneMap,
  getConfigVar,
  API_GEO_CODE
};

