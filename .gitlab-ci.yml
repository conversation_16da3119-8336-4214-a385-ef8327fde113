stages:
  - test
  - build
  - deploy
test:
  tags:
    - road-name
  stage: test
  script:
    - ls
    - echo $CI_PROJECT_DIR
build:
  tags:
    - road-name
  stage: build
  script:
    - cd $CI_PROJECT_DIR
    - sudo docker-compose build
deploy:
  tags:
    - road-name
  stage: deploy
  script:
    - sudo docker stop road-map-webservice
    - sudo docker rm road-map-webservice
    - sudo docker-compose up -d webservice
