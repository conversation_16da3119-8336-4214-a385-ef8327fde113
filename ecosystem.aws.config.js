module.exports = {
  apps: [
    {
      name: "roadname",
      script: "lib/index.js",
      instances: 3,
      exec_mode: "cluster",
      env: {
        NODE_ENV: "production",
        PORT: 3031
      },
      env_production: {
        NODE_ENV: "production",
        PORT: 3031
      },
      env_aws_production: {
        NODE_ENV: "aws-production",
        PORT: 3031
      },
      env_dev: {
        NODE_ENV: "dev",
        PORT: 3031,
        instances: 1
      },
      env_uat: {
        NODE_ENV: "uat",
        PORT: 3031,
        instances: 2
      },
      // Logging configuration
      log_file: "/var/log/ctr-road-name/combined.log",
      out_file: "/var/log/ctr-road-name/out.log",
      error_file: "/var/log/ctr-road-name/error.log",
      log_date_format: "YYYY-MM-DD HH:mm:ss Z",

      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",

      // Advanced features
      min_uptime: "10s",
      max_restarts: 10,

      // Health monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,

      // Environment variables for all environments
      env_common: {
        NODE_OPTIONS: "--max-old-space-size=1024"
      }
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: "ubuntu",
      host: ["production-server"],
      ref: "origin/main",
      repo: "**************:your-repo/ctr-road-name-translation.git",
      path: "/opt/ctr-road-name",
      "post-deploy": "yarn install --production && pm2 reload ecosystem.config.js --env production"
    }
  }
};