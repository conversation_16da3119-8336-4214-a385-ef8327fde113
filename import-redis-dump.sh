#!/usr/bin/env bash

# Redis Import Script
# Imports an existing Redis dump file to the destination server

set -euo pipefail

# Configuration
DEST_HOST="ctr-road-name"
DEST_PORT="6379"
DEST_DB="0"
DUMP_FILE="redis-migration-backup/redis_dump_20250728_104219.txt"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="redis-migration-backup/import_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

# Error handling
error_exit() {
    log "ERROR" "${RED}$1${NC}"
    exit 1
}

# Success message
success() {
    log "INFO" "${GREEN}$1${NC}"
}

# Warning message
warning() {
    log "WARN" "${YELLOW}$1${NC}"
}

# Info message
info() {
    log "INFO" "${BLUE}$1${NC}"
}

# Check if dump file exists
check_dump_file() {
    info "Checking dump file..."
    
    if [ ! -f "${DUMP_FILE}" ]; then
        error_exit "Dump file not found: ${DUMP_FILE}"
    fi
    
    local line_count=$(wc -l < "${DUMP_FILE}")
    local file_size=$(du -h "${DUMP_FILE}" | cut -f1)
    
    info "Dump file found: ${DUMP_FILE}"
    info "File size: ${file_size}"
    info "Total commands: ${line_count}"
    
    success "Dump file validation completed"
}

# Test SSH connection to destination server
test_dest_connection() {
    info "Testing SSH connection to destination server: ${DEST_HOST}"
    
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "${DEST_HOST}" "echo 'SSH connection successful'" &>/dev/null; then
        error_exit "Cannot establish SSH connection to ${DEST_HOST}. Please check your SSH configuration."
    fi
    
    success "SSH connection to destination server successful"
}

# Test Redis connection to destination server
test_dest_redis() {
    info "Testing Redis connection to destination server..."
    
    local redis_test=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} ping 2>/dev/null || echo 'FAILED'")
    
    if [ "$redis_test" != "PONG" ]; then
        error_exit "Cannot connect to Redis on destination server ${DEST_HOST}:${DEST_PORT}"
    fi
    
    success "Redis connection to destination server successful"
}

# Get destination Redis info
get_dest_info() {
    info "Gathering Redis information from destination server..."
    
    local key_count=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dbsize")
    local memory_usage=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r'")
    
    info "Destination Redis Database ${DEST_DB} contains ${key_count} keys"
    info "Destination Redis memory usage: ${memory_usage}"
    
    if [ "$key_count" -gt 0 ]; then
        warning "Destination database is not empty (${key_count} keys). Import will overwrite existing keys with same names."
        read -p "Do you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Import cancelled by user"
            exit 0
        fi
    fi
}

# Import data to destination server
import_to_destination() {
    info "Importing data to destination server..."

    local total_lines=$(grep -v '^#' "${DUMP_FILE}" | wc -l)
    local current_line=0
    local batch_size=100
    local temp_batch_file="redis-migration-backup/import_batch_${TIMESTAMP}.tmp"

    info "Total commands to import: ${total_lines}"
    info "Using batch size: ${batch_size}"

    # Create a temporary script for batch processing
    local batch_script="redis-migration-backup/batch_import_${TIMESTAMP}.sh"
    cat > "${batch_script}" << 'EOF'
#!/bin/bash
DEST_PORT=$1
DEST_DB=$2
BATCH_FILE=$3

while IFS= read -r line; do
    if [[ ! "$line" =~ ^#.* ]] && [[ -n "$line" ]]; then
        echo "$line"
    fi
done < "$BATCH_FILE" | redis-cli -p "$DEST_PORT" -n "$DEST_DB"
EOF
    chmod +x "${batch_script}"

    # Process in batches
    while IFS= read -r line; do
        # Skip comment lines and empty lines
        if [[ "$line" =~ ^#.* ]] || [[ -z "$line" ]]; then
            continue
        fi

        echo "$line" >> "${temp_batch_file}"
        ((current_line++))

        # Process batch when batch_size is reached or at end of file
        if [ $((current_line % batch_size)) -eq 0 ] || [ $current_line -eq $total_lines ]; then
            # Copy batch script and file to destination server
            scp "${batch_script}" "${DEST_HOST}:/tmp/" &>/dev/null
            scp "${temp_batch_file}" "${DEST_HOST}:/tmp/batch_${current_line}.tmp" &>/dev/null

            # Execute batch import on destination server
            if ! ssh "${DEST_HOST}" "/tmp/batch_import_${TIMESTAMP}.sh ${DEST_PORT} ${DEST_DB} /tmp/batch_${current_line}.tmp" &>/dev/null; then
                error_exit "Failed to import batch at line ${current_line}"
            fi

            # Cleanup remote files
            ssh "${DEST_HOST}" "rm -f /tmp/batch_${current_line}.tmp" &>/dev/null

            # Clear local batch file
            > "${temp_batch_file}"

            # Show progress
            local progress=$((current_line * 100 / total_lines))
            printf "\rProgress: %d%% (%d/%d commands)" "$progress" "$current_line" "$total_lines"
        fi
    done < "${DUMP_FILE}"

    echo  # New line after progress

    # Cleanup
    rm -f "${temp_batch_file}" "${batch_script}"
    ssh "${DEST_HOST}" "rm -f /tmp/batch_import_${TIMESTAMP}.sh" &>/dev/null

    success "Data import completed successfully"
}

# Verify import
verify_import() {
    info "Verifying import..."
    
    local dest_count=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dbsize")
    local dest_memory=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r'")
    
    info "Destination database now contains: ${dest_count} keys"
    info "Destination Redis memory usage: ${dest_memory}"
    
    # Sample key verification
    info "Performing sample key verification..."
    local sample_keys=$(grep '^SET' "${DUMP_FILE}" | head -5 | sed 's/SET "\([^"]*\)".*/\1/')
    local verified_count=0
    local total_samples=0
    
    while IFS= read -r key; do
        if [ -n "$key" ]; then
            ((total_samples++))
            local dest_value=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} exists \"$key\"" 2>/dev/null)
            
            if [ "$dest_value" = "1" ]; then
                ((verified_count++))
            else
                warning "Key not found: $key"
            fi
        fi
    done <<< "$sample_keys"
    
    if [ $total_samples -gt 0 ]; then
        info "Sample verification: ${verified_count}/${total_samples} keys verified"
        if [ $verified_count -eq $total_samples ]; then
            success "Sample verification passed"
        else
            warning "Some sample keys failed verification"
        fi
    fi
    
    success "Import verification completed"
}

# Generate import report
generate_report() {
    info "Generating import report..."
    
    local report_file="redis-migration-backup/import_report_${TIMESTAMP}.txt"
    
    cat > "${report_file}" << EOF
Redis Import Report
==================
Date: $(date)
Destination Server: ${DEST_HOST}:${DEST_PORT} (DB ${DEST_DB})
Source Dump File: ${DUMP_FILE}

Import Files:
- Log File: ${LOG_FILE}
- Report File: ${report_file}

Destination Database Info After Import:
$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} info keyspace" 2>/dev/null || echo "Could not retrieve destination info")

Import Status: COMPLETED
EOF

    success "Import report generated: ${report_file}"
}

# Print usage information
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Redis Import Script - Imports Redis dump file to destination server

OPTIONS:
    -f, --file              Dump file path (default: ${DUMP_FILE})
    -d, --dest-host         Destination server hostname (default: ${DEST_HOST})
    -p, --dest-port         Destination Redis port (default: ${DEST_PORT})
    --dest-db               Destination Redis database number (default: ${DEST_DB})
    -h, --help              Show this help message

EXAMPLES:
    $0                                          # Use default settings
    $0 -f /path/to/dump.txt                    # Custom dump file
    $0 -d server2 -p 6380                     # Custom destination

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--file)
                DUMP_FILE="$2"
                shift 2
                ;;
            -d|--dest-host)
                DEST_HOST="$2"
                shift 2
                ;;
            -p|--dest-port)
                DEST_PORT="$2"
                shift 2
                ;;
            --dest-db)
                DEST_DB="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error_exit "Unknown option: $1. Use -h or --help for usage information."
                ;;
        esac
    done
}

# Main import function
main() {
    info "Starting Redis import process..."
    info "Destination: ${DEST_HOST}:${DEST_PORT} (DB ${DEST_DB})"
    info "Dump file: ${DUMP_FILE}"
    
    # Pre-import checks
    check_dump_file
    test_dest_connection
    test_dest_redis
    get_dest_info
    
    # Perform import
    import_to_destination
    verify_import
    generate_report
    
    success "Redis import completed successfully!"
    info "Import files are stored in: redis-migration-backup/"
    info "Check the log file for detailed information: ${LOG_FILE}"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    parse_args "$@"
    
    # Start import
    main
fi
