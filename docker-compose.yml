version: "3.4"
services:

#  redis:
#    container_name: road-name-redis
#    image: redis:3-alpine
#    ports:
#      - "6379:6379"
#    volumes:
#      - ./redis-data:/redis/data
#    networks:
#      - road-name

  webservice:
    container_name: road-map-webservice
    image: node:17-alpine
    ports:
      - 3031:3031
    expose:
      - "3031"
    volumes:
      - type: bind
        source: ./
        target: /app
      - type: volume
        source: nodemodules # name of the volume, see below
        target: /app/node_modules
        volume:
          nocopy: true
    working_dir: /app
    command: sh -c 'npm install --location=global npm@8.13.2 && npm install -f && npm run start-dev'
    networks:
      - trailer


networks:
  trailer:
    external: true

volumes:
  nodemodules:
