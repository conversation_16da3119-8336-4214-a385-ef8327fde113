# AWS Configuration
aws_region = "ap-southeast-1"  # Your preferred region

# Project Configuration
project_name = "ctr-road-name"
environment  = "production"

# Network Configuration
vpc_cidr           = "10.0.0.0/16"
public_subnet_cidr = "********/24"

# EC2 Configuration
instance_type      = "t3a.small"
root_volume_size   = 20
node_version       = "18"
app_port           = 3031

# Security Configuration (IMPORTANT: Restrict these!)
allowed_ssh_cidr_blocks = ["*************/32"]  # Replace with your IP
allowed_app_cidr_blocks = ["0.0.0.0/0"]   # Or restrict as needed

# SSH Key Configuration
create_key_pair    = true
public_key_content = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC2c1KSFWArs1b/08utU0SjGkLGMPLWhO1lIjtG2UnNL60PmAtHeogieOwWnuByjVrbfrwyhDqWA2CDSLQaR1tUXyytNfKgAjmAWJfWfzMT/IRBzoEn8fetw5oJFpkAj6yaEg6uOana/elTPwHOKcfvn7v8XcTyQt3HwoC6FkiWx93eug7YHLI/JWNXzhE45EKf656u0KdiXNsMD+DmdZ8DixFqeAzUYpndFu0nci4nzDifitU5pjoZvfPkKMmf76hJCva/f/O/S5APYI9KwnxiOmI/MAdk9dZRx7YJ0aFfF+7mtkiHyq4BLj4igdknlx3FctcsCo8djeFM72vUiJryAZgAtMqNqynT9O6DS9dZCnYevSmIqwDyZxsLy/XC54N8wDyUZm3VBsPD5XJDTZJ15Yu8NIms6UkksZJ3SAPteagwzkZsnR1rcMA3BsBdH5ddb9/QT5CcUk7rFDDS/QBkJ6fXiWj5BGtRaxuHMng23CN1BVjQ/JuWdBTkZYc9YwAT/1gUg078V/SvlF1RXDxSf1BsAqgIi2/wvjD3LzecSBYI+uH/jOT2gCcJrX6sELG/zgi6DmRcBCAiaoAAndTuFc4TpJ0eNLoKKcu2fuVmtwGVugQKVXMWN43RiGXDmCAWS0ED2DoohxX/7c39iIgPCFb0WdYmvaLl1FxrlFrQxQ== <EMAIL>"