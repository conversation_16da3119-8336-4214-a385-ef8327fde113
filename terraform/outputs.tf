# Outputs for CTR Road Name Translation infrastructure

output "instance_id" {
  description = "ID of the EC2 instance"
  value       = aws_instance.main.id
}

output "instance_public_ip" {
  description = "Public IP address of the EC2 instance"
  value       = aws_eip.main.public_ip
}

output "instance_private_ip" {
  description = "Private IP address of the EC2 instance"
  value       = aws_instance.main.private_ip
}

output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "subnet_id" {
  description = "ID of the public subnet"
  value       = aws_subnet.public.id
}

output "security_group_id" {
  description = "ID of the security group"
  value       = aws_security_group.ec2.id
}

output "ssh_command" {
  description = "SSH command to connect to the instance"
  value       = "ssh -i ~/.ssh/${var.create_key_pair ? aws_key_pair.main[0].key_name : var.existing_key_name}.pem ubuntu@${aws_eip.main.public_ip}"
}

output "application_url" {
  description = "URL to access the application"
  value       = "http://${aws_eip.main.public_ip}:${var.app_port}"
}

output "redis_connection_string" {
  description = "Redis connection string (internal)"
  value       = "redis://${aws_instance.main.private_ip}:6379"
  sensitive   = true
}