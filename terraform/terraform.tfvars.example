# Example Terraform variables file
# Copy this file to terraform.tfvars and modify the values as needed

# AWS Configuration
aws_region = "ap-southeast-1"  # Change to your preferred region

# Project Configuration
project_name = "ctr-road-name"
environment  = "production"

# Network Configuration
vpc_cidr           = "10.0.0.0/16"
public_subnet_cidr = "********/24"

# EC2 Configuration
instance_type      = "t3a.small"
root_volume_size   = 20
node_version       = "18"
app_port           = 3031

# Security Configuration
# IMPORTANT: Change these to your specific IP ranges for better security
allowed_ssh_cidr_blocks = ["0.0.0.0/0"]  # Replace with your IP: ["YOUR_IP/32"]
allowed_app_cidr_blocks = ["0.0.0.0/0"]  # Replace with allowed IPs

# SSH Key Configuration
# Option 1: Create a new key pair (recommended)
create_key_pair    = true
public_key_content = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC... your-public-key-here"

# Option 2: Use existing key pair
# create_key_pair   = false
# existing_key_name = "my-existing-key"

# Example of how to generate a new SSH key pair:
# ssh-keygen -t rsa -b 4096 -f ~/.ssh/ctr-road-name-key
# Then copy the content of ~/.ssh/ctr-road-name-key.pub to public_key_content above