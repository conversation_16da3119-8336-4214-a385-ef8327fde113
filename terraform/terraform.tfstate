{"version": 4, "terraform_version": "1.11.3", "serial": 10, "lineage": "aca96309-65d4-7719-356c-fd5eaa87360c", "outputs": {"application_url": {"value": "http://*************:3031", "type": "string"}, "instance_id": {"value": "i-01ddc349d29e5e0fd", "type": "string"}, "instance_private_ip": {"value": "**********", "type": "string"}, "instance_public_ip": {"value": "*************", "type": "string"}, "redis_connection_string": {"value": "redis://**********:6379", "type": "string", "sensitive": true}, "security_group_id": {"value": "sg-0b475ecbd79f94450", "type": "string"}, "ssh_command": {"value": "ssh -i ~/.ssh/ctr-road-name-key.pem ubuntu@*************", "type": "string"}, "subnet_id": {"value": "subnet-0d601b847fa9a0b68", "type": "string"}, "vpc_id": {"value": "vpc-064859da1da3b3337", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_ami", "name": "ubuntu", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architecture": "x86_64", "arn": "arn:aws:ec2:ap-southeast-1::image/ami-04056160f0fcaaf17", "block_device_mappings": [{"device_name": "/dev/sda1", "ebs": {"delete_on_termination": "true", "encrypted": "false", "iops": "0", "snapshot_id": "snap-08ca6d3138b027d1e", "throughput": "0", "volume_initialization_rate": "0", "volume_size": "8", "volume_type": "gp2"}, "no_device": "", "virtual_name": ""}, {"device_name": "/dev/sdb", "ebs": {}, "no_device": "", "virtual_name": "ephemeral0"}, {"device_name": "/dev/sdc", "ebs": {}, "no_device": "", "virtual_name": "ephemeral1"}], "boot_mode": "uefi-preferred", "creation_date": "2025-07-12T06:57:17.000Z", "deprecation_time": "2027-07-12T06:57:17.000Z", "description": "Canonical, U<PERSON><PERSON>u, 22.04, amd64 jammy image", "ena_support": true, "executable_users": null, "filter": [{"name": "name", "values": ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*"]}, {"name": "virtualization-type", "values": ["hvm"]}], "hypervisor": "xen", "id": "ami-04056160f0fcaaf17", "image_id": "ami-04056160f0fcaaf17", "image_location": "amazon/ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-20250712", "image_owner_alias": "amazon", "image_type": "machine", "imds_support": "", "include_deprecated": false, "kernel_id": "", "last_launched_time": "", "most_recent": true, "name": "ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-20250712", "name_regex": null, "owner_id": "099720109477", "owners": ["099720109477"], "platform": "", "platform_details": "Linux/UNIX", "product_codes": [], "public": true, "ramdisk_id": "", "root_device_name": "/dev/sda1", "root_device_type": "ebs", "root_snapshot_id": "snap-08ca6d3138b027d1e", "sriov_net_support": "simple", "state": "available", "state_reason": {"code": "UNSET", "message": "UNSET"}, "tags": {}, "timeouts": null, "tpm_support": "", "uefi_data": null, "usage_operation": "RunInstances", "virtualization_type": "hvm"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["ap-southeast-1-zg-1"], "id": "ap-southeast-1", "names": ["ap-southeast-1a", "ap-southeast-1b", "ap-southeast-1c"], "state": "available", "timeouts": null, "zone_ids": ["apse1-az1", "apse1-az2", "apse1-az3"]}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_eip", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-08292b38363232c53", "arn": "arn:aws:ec2:ap-southeast-1:147722175163:elastic-ip/eipalloc-08292b38363232c53", "associate_with_private_ip": null, "association_id": "eipassoc-00194ff81be57c082", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-08292b38363232c53", "instance": "i-01ddc349d29e5e0fd", "ipam_pool_id": null, "network_border_group": "ap-southeast-1", "network_interface": "eni-0224d92daca82bc15", "private_dns": "ip-10-0-1-197.ap-southeast-1.compute.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-54-179-253-11.ap-southeast-1.compute.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Environment": "production", "Name": "ctr-road-name-eip", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-eip", "Project": "ctr-road-name"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_instance.main", "aws_internet_gateway.main", "aws_key_pair.main", "aws_security_group.ec2", "aws_subnet.public", "aws_vpc.main", "data.aws_ami.ubuntu", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_instance", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"ami": "ami-04056160f0fcaaf17", "arn": "arn:aws:ec2:ap-southeast-1:147722175163:instance/i-01ddc349d29e5e0fd", "associate_public_ip_address": true, "availability_zone": "ap-southeast-1a", "capacity_reservation_specification": [{"capacity_reservation_preference": "open", "capacity_reservation_target": []}], "cpu_core_count": 1, "cpu_options": [{"amd_sev_snp": "", "core_count": 1, "threads_per_core": 2}], "cpu_threads_per_core": 2, "credit_specification": [{"cpu_credits": "unlimited"}], "disable_api_stop": false, "disable_api_termination": false, "ebs_block_device": [], "ebs_optimized": false, "enable_primary_ipv6": null, "enclave_options": [{"enabled": false}], "ephemeral_block_device": [], "get_password_data": false, "hibernation": false, "host_id": "", "host_resource_group_arn": null, "iam_instance_profile": "", "id": "i-01ddc349d29e5e0fd", "instance_initiated_shutdown_behavior": "stop", "instance_lifecycle": "", "instance_market_options": [], "instance_state": "running", "instance_type": "t3a.small", "ipv6_address_count": 0, "ipv6_addresses": [], "key_name": "ctr-road-name-key", "launch_template": [], "maintenance_options": [{"auto_recovery": "default"}], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "disabled", "http_put_response_hop_limit": 1, "http_tokens": "optional", "instance_metadata_tags": "disabled"}], "monitoring": false, "network_interface": [], "outpost_arn": "", "password_data": "", "placement_group": "", "placement_partition_number": 0, "primary_network_interface_id": "eni-0224d92daca82bc15", "private_dns": "ip-10-0-1-197.ap-southeast-1.compute.internal", "private_dns_name_options": [{"enable_resource_name_dns_a_record": false, "enable_resource_name_dns_aaaa_record": false, "hostname_type": "ip-name"}], "private_ip": "**********", "public_dns": "ec2-18-141-201-246.ap-southeast-1.compute.amazonaws.com", "public_ip": "**************", "root_block_device": [{"delete_on_termination": true, "device_name": "/dev/sda1", "encrypted": true, "iops": 3000, "kms_key_id": "arn:aws:kms:ap-southeast-1:147722175163:key/35f0c2bf-7a01-4ace-9672-d22ea087efdc", "tags": null, "tags_all": {}, "throughput": 125, "volume_id": "vol-08d16095869184856", "volume_size": 20, "volume_type": "gp3"}], "secondary_private_ips": [], "security_groups": [], "source_dest_check": true, "spot_instance_request_id": "", "subnet_id": "subnet-0d601b847fa9a0b68", "tags": {"Environment": "production", "Name": "ctr-road-name-instance", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-instance", "Project": "ctr-road-name"}, "tenancy": "default", "timeouts": null, "user_data": "08452fc21d2533b1f8f42888a7cdd9959f6cf835", "user_data_base64": null, "user_data_replace_on_change": false, "volume_tags": null, "vpc_security_group_ids": ["sg-0b475ecbd79f94450"]}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMCwicmVhZCI6OTAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_key_pair.main", "aws_security_group.ec2", "aws_subnet.public", "aws_vpc.main", "data.aws_ami.ubuntu", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:147722175163:internet-gateway/igw-09707563fcd172a88", "id": "igw-09707563fcd172a88", "owner_id": "147722175163", "tags": {"Environment": "production", "Name": "ctr-road-name-igw", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-igw", "Project": "ctr-road-name"}, "timeouts": null, "vpc_id": "vpc-064859da1da3b3337"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_key_pair", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:147722175163:key-pair/ctr-road-name-key", "fingerprint": "e3:b8:fd:ff:c2:17:15:57:01:ad:f4:31:0c:41:2e:0f", "id": "ctr-road-name-key", "key_name": "ctr-road-name-key", "key_name_prefix": "", "key_pair_id": "key-079739b6bff8b7e67", "key_type": "rsa", "public_key": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC2c1KSFWArs1b/08utU0SjGkLGMPLWhO1lIjtG2UnNL60PmAtHeogieOwWnuByjVrbfrwyhDqWA2CDSLQaR1tUXyytNfKgAjmAWJfWfzMT/IRBzoEn8fetw5oJFpkAj6yaEg6uOana/elTPwHOKcfvn7v8XcTyQt3HwoC6FkiWx93eug7YHLI/JWNXzhE45EKf656u0KdiXNsMD+DmdZ8DixFqeAzUYpndFu0nci4nzDifitU5pjoZvfPkKMmf76hJCva/f/O/S5APYI9KwnxiOmI/MAdk9dZRx7YJ0aFfF+7mtkiHyq4BLj4igdknlx3FctcsCo8djeFM72vUiJryAZgAtMqNqynT9O6DS9dZCnYevSmIqwDyZxsLy/XC54N8wDyUZm3VBsPD5XJDTZJ15Yu8NIms6UkksZJ3SAPteagwzkZsnR1rcMA3BsBdH5ddb9/QT5CcUk7rFDDS/QBkJ6fXiWj5BGtRaxuHMng23CN1BVjQ/JuWdBTkZYc9YwAT/1gUg078V/SvlF1RXDxSf1BsAqgIi2/wvjD3LzecSBYI+uH/jOT2gCcJrX6sELG/zgi6DmRcBCAiaoAAndTuFc4TpJ0eNLoKKcu2fuVmtwGVugQKVXMWN43RiGXDmCAWS0ED2DoohxX/7c39iIgPCFb0WdYmvaLl1FxrlFrQxQ== <EMAIL>", "tags": {"Environment": "production", "Name": "ctr-road-name-key", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-key", "Project": "ctr-road-name"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:147722175163:route-table/rtb-0c2caeddb9fa1cde5", "id": "rtb-0c2caeddb9fa1cde5", "owner_id": "147722175163", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-09707563fcd172a88", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "production", "Name": "ctr-road-name-public-rt", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-public-rt", "Project": "ctr-road-name"}, "timeouts": null, "vpc_id": "vpc-064859da1da3b3337"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-09d23de2ddc0ba186", "route_table_id": "rtb-0c2caeddb9fa1cde5", "subnet_id": "subnet-0d601b847fa9a0b68", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "ec2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:147722175163:security-group/sg-0b475ecbd79f94450", "description": "Security group for CTR Road Name Translation EC2 instance", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0b475ecbd79f94450", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "HTTP", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}, {"cidr_blocks": ["0.0.0.0/0"], "description": "HTTPS", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}, {"cidr_blocks": ["0.0.0.0/0"], "description": "Node.js App", "from_port": 3031, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 3031}, {"cidr_blocks": ["10.0.0.0/16"], "description": "Redis", "from_port": 6379, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 6379}, {"cidr_blocks": ["*************/32"], "description": "SSH", "from_port": 22, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 22}], "name": "ctr-road-name-ec2-20250723095649637200000001", "name_prefix": "ctr-road-name-ec2-", "owner_id": "147722175163", "revoke_rules_on_delete": false, "tags": {"Environment": "production", "Name": "ctr-road-name-ec2-sg", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-ec2-sg", "Project": "ctr-road-name"}, "timeouts": null, "vpc_id": "vpc-064859da1da3b3337"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:147722175163:subnet/subnet-0d601b847fa9a0b68", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-southeast-1a", "availability_zone_id": "apse1-az1", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0d601b847fa9a0b68", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "147722175163", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "production", "Name": "ctr-road-name-public-subnet", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-public-subnet", "Project": "ctr-road-name"}, "timeouts": null, "vpc_id": "vpc-064859da1da3b3337"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-southeast-1:147722175163:vpc/vpc-064859da1da3b3337", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-055cad41c714107b1", "default_route_table_id": "rtb-0a8d4062f9245e977", "default_security_group_id": "sg-0e269f4d88c7dfad6", "dhcp_options_id": "dopt-061af284dc6064fa7", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-064859da1da3b3337", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0a8d4062f9245e977", "owner_id": "147722175163", "tags": {"Environment": "production", "Name": "ctr-road-name-vpc", "Project": "ctr-road-name"}, "tags_all": {"Environment": "production", "Name": "ctr-road-name-vpc", "Project": "ctr-road-name"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}