{"name": "ctr-road-map-caching", "description": "", "version": "0.0.0", "homepage": "", "private": true, "main": "src", "keywords": ["feathers"], "author": {"name": "thangto", "email": "<EMAIL>"}, "contributors": [], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "scripts": {"test": "npm run lint && npm run compile && npm run jest", "lint": "eslint src/. test/. --config .eslintrc.json --ext .ts --fix", "start-dev": "npm run compile && NODE_ENV=dev-server node lib/", "dev": "ts-node-dev --no-notify src/", "start": "npm run compile && node lib/", "jest": "jest --force<PERSON>xit", "compile": "swc src -d lib --copy-files", "docs": "apidoc -i src/services/*/ -o public/docs/"}, "standard": {"env": ["jest"], "ignore": []}, "types": "lib/", "dependencies": {"@feathersjs/authentication": "^4.5.12", "@feathersjs/authentication-local": "^4.5.12", "@feathersjs/authentication-oauth": "^4.5.12", "@feathersjs/configuration": "^4.5.15", "@feathersjs/errors": "^4.5.15", "@feathersjs/socketio": "^4.5.15", "@types/connect-redis": "^0.0.18", "@types/express-session": "^1.17.4", "@types/multer-s3": "^3.0.0", "@types/socket.io-parser": "^3.0.0", "apidoc": "^0.51.1", "await-to-js": "^3.0.0", "aws-sdk": "^2.1168.0", "axios": "^0.27.2", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^2.0.0", "csvtojson": "^2.0.10", "feathers-mongoose": "^8.5.1", "fs": "^0.0.1-security", "helmet": "^4.6.0", "install": "^0.13.0", "milliseconds": "^1.0.3", "moment": "^2.29.3", "mongodb-core": "^3.2.7", "mongoose": "^6.3.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^2.0.1", "nodemon": "^3.0.1", "npm": "^8.13.2", "path": "^0.12.7", "redis": "^4.1.0", "redis-dump": "^0.1.10", "serve-favicon": "^2.5.0", "ts-node": "^10.8.1", "typescript": "^4.7.4", "validate-typescript": "^4.0.2", "winston": "^3.3.3"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.3.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/cron": "^2.0.0", "@types/jest": "^27.0.3", "@types/jsonwebtoken": "^8.5.8", "@types/milliseconds": "^0.0.30", "@types/morgan": "^1.9.9", "@types/redis": "^4.0.11", "@types/serve-favicon": "^2.5.3", "serverless": "^3.1.1", "shx": "^0.3.4", "ts-node-dev": "^2.0.0"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}