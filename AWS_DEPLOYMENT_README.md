# AWS Deployment Guide for CTR Road Name Translation

This guide provides comprehensive instructions for deploying the CTR Road Name Translation application to AWS using Terraform and automated deployment scripts.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Setup](#infrastructure-setup)
3. [Application Deployment](#application-deployment)
4. [Configuration](#configuration)
5. [Monitoring and Maintenance](#monitoring-and-maintenance)
6. [Troubleshooting](#troubleshooting)
7. [Security Considerations](#security-considerations)

## Prerequisites

### Required Software

- **Terraform** (>= 1.0): [Installation Guide](https://learn.hashicorp.com/tutorials/terraform/install-cli)
- **AWS CLI** (>= 2.0): [Installation Guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
- **SSH Client**: For connecting to EC2 instances
- **Node.js** (>= 18): For local development and building
- **Yarn**: Package manager

### AWS Account Setup

1. **AWS Account**: Ensure you have an active AWS account
2. **IAM User**: Create an IAM user with the following permissions:
   - EC2FullAccess
   - VPCFullAccess
   - IAMReadOnlyAccess (for key pair management)
3. **AWS CLI Configuration**:
   ```bash
   aws configure
   # Enter your Access Key ID, Secret Access Key, and preferred region
   ```

### SSH Key Pair

Generate an SSH key pair for EC2 access:
```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/ctr-road-name-key
chmod 600 ~/.ssh/ctr-road-name-key
```

## Infrastructure Setup

### 1. Configure Terraform Variables

Copy the example variables file and customize it:
```bash
cd terraform
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` with your specific values:
```hcl
# AWS Configuration
aws_region = "ap-southeast-1"  # Your preferred region

# Project Configuration
project_name = "ctr-road-name"
environment  = "production"

# Network Configuration
vpc_cidr           = "10.0.0.0/16"
public_subnet_cidr = "********/24"

# EC2 Configuration
instance_type      = "t3a.small"
root_volume_size   = 20
node_version       = "18"
app_port           = 3031

# Security Configuration (IMPORTANT: Restrict these!)
allowed_ssh_cidr_blocks = ["YOUR_IP/32"]  # Replace with your IP
allowed_app_cidr_blocks = ["0.0.0.0/0"]   # Or restrict as needed

# SSH Key Configuration
create_key_pair    = true
public_key_content = "ssh-rsa AAAAB3NzaC1yc2E... [content of ~/.ssh/ctr-road-name-key.pub]"
```

### 2. Initialize and Deploy Infrastructure

```bash
cd terraform

# Initialize Terraform
terraform init

# Review the planned changes
terraform plan

# Apply the infrastructure
terraform apply
```

### 3. Note the Outputs

After successful deployment, Terraform will output important information:
```
instance_public_ip = "*******"
ssh_command = "ssh -i ~/.ssh/ctr-road-name-key ubuntu@*******"
application_url = "http://*******:3031"
```

## Application Deployment

### 1. Wait for Instance Initialization

The EC2 instance needs time to complete the user data script. Check the status:
```bash
# SSH to the instance
ssh -i ~/.ssh/ctr-road-name-key ubuntu@YOUR_INSTANCE_IP

# Check user data script completion
sudo tail -f /var/log/user-data.log

# Verify services are running
sudo systemctl status redis-server
pm2 status
```

### 2. Deploy the Application

Use the automated deployment script:
```bash
# From the project root directory
./deploy_aws.sh --host YOUR_INSTANCE_IP --key ~/.ssh/ctr-road-name-key --env aws-production
```

### 3. Verify Deployment

Check that the application is running:
```bash
# Check PM2 status
ssh -i ~/.ssh/ctr-road-name-key ctrapp@YOUR_INSTANCE_IP "pm2 status"

# Run health check
ssh -i ~/.ssh/ctr-road-name-key ctrapp@YOUR_INSTANCE_IP "/opt/ctr-road-name/scripts/health-check.sh"

# Test the application
curl http://YOUR_INSTANCE_IP:3031
```

## Configuration

### Environment-Specific Configuration

The application supports multiple environment configurations:

- `config/production.json` - Original production configuration
- `config/aws-production.json` - AWS-optimized production configuration
- `config/dev.json` - Development configuration
- `config/uat.json` - UAT configuration

### Key Configuration Changes for AWS

The `aws-production.json` configuration includes:
- Host binding to `0.0.0.0` for external access
- Structured logging to `/var/log/ctr-road-name/`
- Redis connection to localhost
- AWS region set to `ap-southeast-1`

### PM2 Configuration

The `ecosystem.aws.config.js` file provides:
- Multiple environment support
- Cluster mode with 3 instances
- Automatic restart on failure
- Log rotation and management
- Memory limit monitoring

## Monitoring and Maintenance

### Health Checks

Use the provided health check script:
```bash
# On the server
./scripts/health-check.sh

# Remote health check
ssh -i ~/.ssh/ctr-road-name-key ctrapp@YOUR_INSTANCE_IP "/opt/ctr-road-name/scripts/health-check.sh"
```

### Log Management

Application logs are stored in `/var/log/ctr-road-name/`:
```bash
# View application logs
sudo tail -f /var/log/ctr-road-name/application.log

# View error logs
sudo tail -f /var/log/ctr-road-name/error.log

# View PM2 logs
pm2 logs roadname

# View system logs
sudo journalctl -u pm2-ctrapp -f
```

### PM2 Management Commands

```bash
# Check status
pm2 status

# Restart application
pm2 restart roadname

# Stop application
pm2 stop roadname

# View logs
pm2 logs roadname

# Monitor resources
pm2 monit

# Reload application (zero-downtime)
pm2 reload roadname
```

### System Monitoring

```bash
# Check system resources
htop

# Check disk usage
df -h

# Check memory usage
free -h

# Check Redis status
redis-cli ping
redis-cli info

# Check network connections
netstat -tuln | grep 3031
```

### Backup and Recovery

#### Database Backup (if using MongoDB)
```bash
# Create backup
mongodump --out /backup/mongodb/$(date +%Y%m%d_%H%M%S)

# Restore backup
mongorestore /backup/mongodb/BACKUP_FOLDER
```

#### Redis Backup
```bash
# Create Redis backup
redis-cli BGSAVE

# Copy RDB file
sudo cp /var/lib/redis/dump.rdb /backup/redis/dump_$(date +%Y%m%d_%H%M%S).rdb
```

#### Application Backup
```bash
# Backup application directory
sudo tar -czf /backup/app/ctr-road-name_$(date +%Y%m%d_%H%M%S).tar.gz /opt/ctr-road-name
```

## Troubleshooting

### Common Issues

#### 1. Application Not Starting
```bash
# Check PM2 logs
pm2 logs roadname

# Check if port is in use
sudo netstat -tuln | grep 3031

# Check Node.js version
node --version

# Check application dependencies
cd /opt/ctr-road-name && yarn install
```

#### 2. Redis Connection Issues
```bash
# Check Redis status
sudo systemctl status redis-server

# Test Redis connection
redis-cli ping

# Check Redis configuration
sudo cat /etc/redis/redis.conf | grep bind

# Restart Redis
sudo systemctl restart redis-server
```

#### 3. Memory Issues
```bash
# Check memory usage
free -h

# Check PM2 memory usage
pm2 monit

# Restart application to free memory
pm2 restart roadname
```

#### 4. Disk Space Issues
```bash
# Check disk usage
df -h

# Clean up logs
sudo find /var/log -name "*.log" -type f -size +100M -delete

# Clean up PM2 logs
pm2 flush
```

#### 5. Network Connectivity Issues
```bash
# Check security group rules in AWS Console
# Verify firewall rules
sudo ufw status

# Test port connectivity
telnet YOUR_INSTANCE_IP 3031

# Check if application is binding to correct interface
sudo netstat -tuln | grep 3031
```

### Deployment Script Issues

#### Permission Denied
```bash
# Make sure the script is executable
chmod +x deploy_aws.sh

# Check SSH key permissions
chmod 600 ~/.ssh/ctr-road-name-key
```

#### SSH Connection Failed
```bash
# Test SSH connection manually
ssh -i ~/.ssh/ctr-road-name-key ubuntu@YOUR_INSTANCE_IP

# Check security group allows SSH from your IP
# Verify the correct SSH key is being used
```

#### Build Failures
```bash
# Clean and rebuild
rm -rf node_modules build
yarn install
yarn compile
```

## Security Considerations

### Network Security

1. **Restrict SSH Access**: Update `allowed_ssh_cidr_blocks` in `terraform.tfvars` to your specific IP range
2. **Application Access**: Consider restricting `allowed_app_cidr_blocks` to known client IPs
3. **VPC Security**: The application runs in a dedicated VPC with controlled access

### Instance Security

1. **Regular Updates**: Keep the system updated
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **Fail2ban**: Automatically configured to protect against brute force attacks

3. **Firewall**: UFW is configured with minimal required ports open

4. **User Permissions**: Application runs under dedicated `ctrapp` user

### Application Security

1. **Environment Variables**: Store sensitive data in environment variables, not in code
2. **HTTPS**: Consider setting up SSL/TLS termination with a load balancer or reverse proxy
3. **Authentication**: Ensure proper authentication mechanisms are in place
4. **Input Validation**: Validate all input data

### AWS Security

1. **IAM Roles**: Use IAM roles instead of access keys when possible
2. **Security Groups**: Regularly review and update security group rules
3. **Encryption**: EBS volumes are encrypted by default
4. **Monitoring**: Enable CloudWatch monitoring and logging

## Cost Optimization

### Instance Sizing
- Start with `t3a.small` and monitor usage
- Consider `t3a.micro` for development environments
- Use `t3a.medium` or larger for high-traffic production

### Reserved Instances
- Consider Reserved Instances for long-term deployments
- Savings Plans can provide flexibility with cost savings

### Monitoring Costs
- Use AWS Cost Explorer to monitor spending
- Set up billing alerts for unexpected charges

## Scaling Considerations

### Vertical Scaling
```bash
# Stop the application
pm2 stop roadname

# In AWS Console, stop the instance and change instance type
# Start the instance and application
pm2 start roadname
```

### Horizontal Scaling
- Consider using an Application Load Balancer
- Deploy multiple instances across availability zones
- Use Auto Scaling Groups for automatic scaling

### Database Scaling
- Consider Amazon RDS for managed database
- Implement read replicas for read-heavy workloads
- Use Amazon ElastiCache for Redis scaling

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**:
   - Check application logs for errors
   - Monitor system resources
   - Verify backups are working

2. **Monthly**:
   - Update system packages
   - Review security group rules
   - Analyze cost reports

3. **Quarterly**:
   - Review and update dependencies
   - Performance testing
   - Disaster recovery testing

### Getting Help

- **AWS Support**: Use AWS Support for infrastructure issues
- **Application Logs**: Check `/var/log/ctr-road-name/` for application-specific issues
- **PM2 Documentation**: [PM2 Official Documentation](https://pm2.keymetrics.io/docs/)
- **Terraform Documentation**: [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)

---

## Quick Reference Commands

### Terraform Commands
```bash
terraform init          # Initialize Terraform
terraform plan          # Preview changes
terraform apply         # Apply changes
terraform destroy       # Destroy infrastructure
terraform output        # Show outputs
```

### Deployment Commands
```bash
./deploy_aws.sh --host IP --key KEY --env aws-production  # Deploy application
./scripts/health-check.sh                                 # Health check
```

### PM2 Commands
```bash
pm2 status              # Show status
pm2 restart roadname    # Restart app
pm2 logs roadname       # View logs
pm2 monit              # Monitor resources
```

### System Commands
```bash
sudo systemctl status redis-server  # Check Redis
sudo ufw status                     # Check firewall
df -h                              # Check disk space
free -h                            # Check memory
```

This completes the comprehensive AWS deployment guide for your CTR Road Name Translation application.