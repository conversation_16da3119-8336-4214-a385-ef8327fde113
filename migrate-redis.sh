#!/usr/bin/env bash

# Redis Migration Script
# Migrates all Redis keys from source Redis server to destination Redis server
# Source: cdas_prod_ets_redis_0
# Destination: ctr-road-name

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
SOURCE_SERVER="cdas_prod_ets_redis_0"
DEST_HOST="ctr-road-name"
DEST_PORT="6379"
SOURCE_PORT="6379"
SOURCE_DB="0"
DEST_DB="0"
BACKUP_DIR="./redis-migration-backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DUMP_FILE="${BACKUP_DIR}/redis_dump_${TIMESTAMP}.txt"
JSON_DUMP_FILE="${BACKUP_DIR}/redis_dump_${TIMESTAMP}.json"
LOG_FILE="${BACKUP_DIR}/migration_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

# Error handling
error_exit() {
    log "ERROR" "${RED}$1${NC}"
    exit 1
}

# Success message
success() {
    log "INFO" "${GREEN}$1${NC}"
}

# Warning message
warning() {
    log "WARN" "${YELLOW}$1${NC}"
}

# Info message
info() {
    log "INFO" "${BLUE}$1${NC}"
}

# Check if required commands exist
check_dependencies() {
    info "Checking dependencies..."

    local deps=("ssh" "redis-cli" "node")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            error_exit "Required command '$dep' not found. Please install it first."
        fi
    done

    # Check if redis-dump is available
    if [ ! -f "./node_modules/redis-dump/bin/cli.js" ]; then
        error_exit "redis-dump not found in node_modules. Please run 'npm install' first."
    fi

    success "All dependencies are available"
}

# Create backup directory
setup_backup_dir() {
    info "Setting up backup directory..."
    mkdir -p "${BACKUP_DIR}"
    success "Backup directory created: ${BACKUP_DIR}"
}

# Test SSH connection to source server
test_source_connection() {
    info "Testing SSH connection to source server: ${SOURCE_SERVER}"

    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "${SOURCE_SERVER}" "echo 'SSH connection successful'" &>/dev/null; then
        error_exit "Cannot establish SSH connection to ${SOURCE_SERVER}. Please check your SSH configuration."
    fi

    success "SSH connection to source server successful"
}

# Test Redis connection on source server
test_source_redis() {
    info "Testing Redis connection on source server..."

    local redis_test=$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} ping 2>/dev/null || echo 'FAILED'")

    if [ "$redis_test" != "PONG" ]; then
        error_exit "Cannot connect to Redis on source server ${SOURCE_SERVER}:${SOURCE_PORT}"
    fi

    success "Redis connection to source server successful"
}

# Test SSH connection to destination server
test_dest_connection() {
    info "Testing SSH connection to destination server: ${DEST_HOST}"

    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "${DEST_HOST}" "echo 'SSH connection successful'" &>/dev/null; then
        error_exit "Cannot establish SSH connection to ${DEST_HOST}. Please check your SSH configuration."
    fi

    success "SSH connection to destination server successful"
}

# Test Redis connection to destination server
test_dest_redis() {
    info "Testing Redis connection to destination server..."

    local redis_test=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} ping 2>/dev/null || echo 'FAILED'")

    if [ "$redis_test" != "PONG" ]; then
        error_exit "Cannot connect to Redis on destination server ${DEST_HOST}:${DEST_PORT}"
    fi

    success "Redis connection to destination server successful"
}

# Get Redis info from source
get_source_info() {
    info "Gathering Redis information from source server..."

    local key_count=$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} dbsize")
    local memory_usage=$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r'")

    info "Source Redis Database ${SOURCE_DB} contains ${key_count} keys"
    info "Source Redis memory usage: ${memory_usage}"

    if [ "$key_count" -eq 0 ]; then
        warning "Source database is empty. Nothing to migrate."
        exit 0
    fi
}

# Get Redis info from destination
get_dest_info() {
    info "Gathering Redis information from destination server..."

    local key_count=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dbsize")
    local memory_usage=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r'")

    info "Destination Redis Database ${DEST_DB} contains ${key_count} keys"
    info "Destination Redis memory usage: ${memory_usage}"

    if [ "$key_count" -gt 0 ]; then
        warning "Destination database is not empty (${key_count} keys). Migration will overwrite existing keys with same names."
        read -p "Do you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "Migration cancelled by user"
            exit 0
        fi
    fi
}

# Dump Redis data from source server
dump_source_data() {
    info "Dumping Redis data from source server..."

    # Create a script to run redis-dump on the source server
    local remote_dump_script="/tmp/redis_dump_script_${TIMESTAMP}.sh"

    # Check if redis-dump is available on source server, if not use redis-cli
    info "Checking for redis-dump availability on source server..."
    if ssh "${SOURCE_SERVER}" "command -v redis-dump" &>/dev/null; then
        local has_redis_dump=true
    else
        local has_redis_dump=false
        warning "redis-dump not found on source server, will use redis-cli method"
    fi

    if [ "$has_redis_dump" = "true" ]; then
        info "Using redis-dump on source server..."
        info "This may take several minutes for large datasets (${SOURCE_SERVER} has 302,670+ keys)..."

        # Add timeout and progress monitoring for large datasets
        local dump_timeout=1800  # 30 minutes timeout

        # Run redis-dump with timeout and better error handling
        info "Starting redis-dump with ${dump_timeout}s timeout..."
        if timeout ${dump_timeout} ssh "${SOURCE_SERVER}" "redis-dump -p ${SOURCE_PORT} -d ${SOURCE_DB}" > "${DUMP_FILE}" 2>"${BACKUP_DIR}/dump_error_${TIMESTAMP}.log"; then
            info "Redis dump completed successfully"
        else
            local exit_code=$?
            if [ $exit_code -eq 124 ]; then
                error_exit "Redis dump timed out after ${dump_timeout} seconds. Dataset might be too large. Check ${BACKUP_DIR}/dump_error_${TIMESTAMP}.log for details."
            else
                error_exit "Failed to dump data using redis-dump from source server (exit code: $exit_code). Check ${BACKUP_DIR}/dump_error_${TIMESTAMP}.log for details."
            fi
        fi

        # Create JSON backup only if main dump succeeded and is not too large
        local dump_size=$(wc -l < "${DUMP_FILE}" 2>/dev/null || echo "0")
        if [ "$dump_size" -lt 50000 ]; then
            info "Creating JSON backup..."
            if timeout 600 ssh "${SOURCE_SERVER}" "redis-dump -p ${SOURCE_PORT} -d ${SOURCE_DB} --json --pretty" > "${JSON_DUMP_FILE}" 2>/dev/null; then
                success "JSON backup created at ${JSON_DUMP_FILE}"
            else
                warning "JSON backup failed or timed out, but main dump succeeded"
            fi
        else
            warning "Skipping JSON backup due to large dataset size (${dump_size} commands)"
        fi
    else
        info "redis-dump not available on source server, using redis-cli method..."
        dump_with_redis_cli
    fi

    if [ ! -s "${DUMP_FILE}" ]; then
        error_exit "Dump file is empty or was not created successfully"
    fi

    local dump_size=$(wc -l < "${DUMP_FILE}")
    success "Successfully dumped ${dump_size} Redis commands to ${DUMP_FILE}"
}

# Alternative dump method using redis-cli for when redis-dump is not available
dump_with_redis_cli() {
    info "Using redis-cli SCAN method for data export..."
    warning "This method is slower but more reliable for large datasets"

    local temp_script="/tmp/redis_export_${TIMESTAMP}.sh"
    local remote_output="/tmp/redis_dump_${TIMESTAMP}.txt"

    # Create export script that will run on source server
    cat > "${temp_script}" << 'EOF'
#!/bin/bash
PORT=$1
DB=$2
OUTPUT_FILE=$3

echo "Starting redis-cli export..." >&2
echo "# Redis export started at $(date)" > "$OUTPUT_FILE"

# Get total key count for progress
total_keys=$(redis-cli -p $PORT -n $DB dbsize)
echo "# Total keys to export: $total_keys" >> "$OUTPUT_FILE"
echo "Total keys to export: $total_keys" >&2

current_key=0

redis-cli -p $PORT -n $DB --scan | while IFS= read -r key; do
    if [ -n "$key" ]; then
        current_key=$((current_key + 1))

        # Show progress every 1000 keys
        if [ $((current_key % 1000)) -eq 0 ]; then
            echo "Processed $current_key/$total_keys keys..." >&2
        fi

        # Get key type
        key_type=$(redis-cli -p $PORT -n $DB type "$key")

        # Escape key for safe usage
        escaped_key=$(printf '%s\n' "$key" | sed 's/[[\.*^$()+?{|]/\\&/g')

        case $key_type in
            "string")
                value=$(redis-cli -p $PORT -n $DB get "$key")
                echo "SET \"$key\" \"$value\"" >> "$OUTPUT_FILE"
                ;;
            "list")
                echo "DEL \"$key\"" >> "$OUTPUT_FILE"
                redis-cli -p $PORT -n $DB lrange "$key" 0 -1 | while IFS= read -r item; do
                    echo "RPUSH \"$key\" \"$item\"" >> "$OUTPUT_FILE"
                done
                ;;
            "set")
                echo "DEL \"$key\"" >> "$OUTPUT_FILE"
                redis-cli -p $PORT -n $DB smembers "$key" | while IFS= read -r member; do
                    echo "SADD \"$key\" \"$member\"" >> "$OUTPUT_FILE"
                done
                ;;
            "hash")
                echo "DEL \"$key\"" >> "$OUTPUT_FILE"
                redis-cli -p $PORT -n $DB hgetall "$key" | while IFS= read -r field && IFS= read -r value; do
                    echo "HSET \"$key\" \"$field\" \"$value\"" >> "$OUTPUT_FILE"
                done
                ;;
            "zset")
                echo "DEL \"$key\"" >> "$OUTPUT_FILE"
                redis-cli -p $PORT -n $DB zrange "$key" 0 -1 withscores | while IFS= read -r member && IFS= read -r score; do
                    echo "ZADD \"$key\" $score \"$member\"" >> "$OUTPUT_FILE"
                done
                ;;
        esac

        # Handle TTL
        ttl=$(redis-cli -p $PORT -n $DB ttl "$key")
        if [ "$ttl" -gt 0 ]; then
            echo "EXPIRE \"$key\" $ttl" >> "$OUTPUT_FILE"
        fi
    fi
done

echo "# Redis export completed at $(date)" >> "$OUTPUT_FILE"
echo "Export completed!" >&2
EOF

    # Copy script to source server and execute
    info "Copying export script to source server..."
    scp "${temp_script}" "${SOURCE_SERVER}:/tmp/" || error_exit "Failed to copy export script to source server"
    ssh "${SOURCE_SERVER}" "chmod +x /tmp/redis_export_${TIMESTAMP}.sh" || error_exit "Failed to make export script executable"

    info "Starting redis-cli export (this will take a while for large datasets)..."
    if ssh "${SOURCE_SERVER}" "/tmp/redis_export_${TIMESTAMP}.sh ${SOURCE_PORT} ${SOURCE_DB} ${remote_output}"; then
        info "Export completed, downloading dump file..."
        # Download the dump file
        scp "${SOURCE_SERVER}:${remote_output}" "${DUMP_FILE}" || error_exit "Failed to download dump file"

        # Cleanup remote files
        ssh "${SOURCE_SERVER}" "rm -f /tmp/redis_export_${TIMESTAMP}.sh ${remote_output}"
        rm -f "${temp_script}"

        success "Data exported using redis-cli method"
    else
        error_exit "Failed to export data using redis-cli method"
    fi
}

# Validate dump file
validate_dump() {
    info "Validating dump file..."

    if [ ! -f "${DUMP_FILE}" ]; then
        error_exit "Dump file not found: ${DUMP_FILE}"
    fi

    local line_count=$(wc -l < "${DUMP_FILE}")
    if [ "$line_count" -eq 0 ]; then
        error_exit "Dump file is empty"
    fi

    # Check if file contains valid Redis commands
    local invalid_lines=$(grep -v -E '^(SET|GET|DEL|RPUSH|LPUSH|SADD|ZADD|HMSET|HSET|EXPIRE|EXPIREAT|PEXPIRE|PEXPIREAT)' "${DUMP_FILE}" | wc -l)
    if [ "$invalid_lines" -gt 0 ]; then
        warning "Found ${invalid_lines} lines that may not be valid Redis commands"
    fi

    success "Dump file validation completed: ${line_count} lines"
}

# Create backup of destination database
backup_destination() {
    info "Creating backup of destination database..."

    local dest_backup_file="${BACKUP_DIR}/destination_backup_${TIMESTAMP}.txt"
    local dest_key_count=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dbsize")

    if [ "$dest_key_count" -gt 0 ]; then
        info "Backing up ${dest_key_count} keys from destination..."

        # Check if redis-dump is available on destination server
        if ssh "${DEST_HOST}" "command -v redis-dump" &>/dev/null; then
            ssh "${DEST_HOST}" "redis-dump -p ${DEST_PORT} -d ${DEST_DB}" > "${dest_backup_file}" 2>/dev/null
        else
            # Fallback: use redis-cli to create a simple backup
            ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} --scan" | while read -r key; do
                if [ -n "$key" ]; then
                    echo "# Key: $key" >> "${dest_backup_file}"
                    ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dump \"$key\"" >> "${dest_backup_file}"
                fi
            done
        fi

        if [ -s "${dest_backup_file}" ]; then
            success "Destination backup created: ${dest_backup_file}"
        else
            warning "Failed to create destination backup, but continuing with migration..."
        fi
    else
        info "Destination database is empty, no backup needed"
    fi
}

# Import data to destination server
import_to_destination() {
    info "Importing data to destination server..."

    local total_lines=$(wc -l < "${DUMP_FILE}")
    local current_line=0
    local batch_size=100
    local temp_batch_file="${BACKUP_DIR}/batch_${TIMESTAMP}.tmp"

    info "Total commands to import: ${total_lines}"

    # Process in batches for better performance and progress tracking
    while IFS= read -r line; do
        echo "$line" >> "${temp_batch_file}"
        ((current_line++))

        # Process batch when batch_size is reached or at end of file
        if [ $((current_line % batch_size)) -eq 0 ] || [ $current_line -eq $total_lines ]; then
            # Import batch via SSH
            if ! cat "${temp_batch_file}" | ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} --pipe" &>/dev/null; then
                error_exit "Failed to import batch at line ${current_line}"
            fi

            # Clear batch file
            > "${temp_batch_file}"

            # Show progress
            local progress=$((current_line * 100 / total_lines))
            printf "\rProgress: %d%% (%d/%d commands)" "$progress" "$current_line" "$total_lines"
        fi
    done < "${DUMP_FILE}"

    echo  # New line after progress
    rm -f "${temp_batch_file}"

    success "Data import completed successfully"
}

# Verify migration
verify_migration() {
    info "Verifying migration..."

    local source_count=$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} dbsize")
    local dest_count=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} dbsize")

    info "Source database key count: ${source_count}"
    info "Destination database key count: ${dest_count}"

    if [ "$source_count" -eq "$dest_count" ]; then
        success "Key count verification passed"
    else
        warning "Key count mismatch! Source: ${source_count}, Destination: ${dest_count}"
    fi

    # Sample key verification
    info "Performing sample key verification..."
    local sample_keys=$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} --scan | head -5")
    local verified_count=0
    local total_samples=0

    while IFS= read -r key; do
        if [ -n "$key" ]; then
            ((total_samples++))
            local source_value=$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} get \"$key\"" 2>/dev/null)
            local dest_value=$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} get \"$key\"" 2>/dev/null)

            if [ "$source_value" = "$dest_value" ]; then
                ((verified_count++))
            else
                warning "Value mismatch for key: $key"
            fi
        fi
    done <<< "$sample_keys"

    if [ $total_samples -gt 0 ]; then
        info "Sample verification: ${verified_count}/${total_samples} keys verified"
        if [ $verified_count -eq $total_samples ]; then
            success "Sample verification passed"
        else
            warning "Some sample keys failed verification"
        fi
    fi
}

# Cleanup temporary files
cleanup() {
    info "Cleaning up temporary files..."

    # Remove temporary files but keep backups and logs
    find "${BACKUP_DIR}" -name "*.tmp" -delete 2>/dev/null || true

    success "Cleanup completed"
}

# Generate migration report
generate_report() {
    info "Generating migration report..."

    local report_file="${BACKUP_DIR}/migration_report_${TIMESTAMP}.txt"

    cat > "${report_file}" << EOF
Redis Migration Report
=====================
Date: $(date)
Source Server: ${SOURCE_SERVER}:${SOURCE_PORT} (DB ${SOURCE_DB})
Destination Server: ${DEST_HOST}:${DEST_PORT} (DB ${DEST_DB})

Migration Files:
- Dump File: ${DUMP_FILE}
- JSON Backup: ${JSON_DUMP_FILE}
- Log File: ${LOG_FILE}

Source Database Info:
$(ssh "${SOURCE_SERVER}" "redis-cli -p ${SOURCE_PORT} -n ${SOURCE_DB} info keyspace" 2>/dev/null || echo "Could not retrieve source info")

Destination Database Info:
$(ssh "${DEST_HOST}" "redis-cli -p ${DEST_PORT} -n ${DEST_DB} info keyspace" 2>/dev/null || echo "Could not retrieve destination info")

Migration Status: COMPLETED
EOF

    success "Migration report generated: ${report_file}"
}

# Print usage information
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Redis Migration Script - Migrates all data from source to destination Redis server

OPTIONS:
    -s, --source-server     Source server hostname (default: cdas_prod_ets_redis_0)
    -d, --dest-host         Destination server IP/hostname (default: ctr-road-name)
    -p, --dest-port         Destination Redis port (default: 6379)
    --source-port           Source Redis port (default: 6379)
    --source-db             Source Redis database number (default: 0)
    --dest-db               Destination Redis database number (default: 0)
    --backup-dir            Backup directory (default: ./redis-migration-backup)
    --dry-run               Perform all checks but don't actually migrate data
    -h, --help              Show this help message

EXAMPLES:
    $0                                          # Use default settings
    $0 -d ************* -p 6380               # Custom destination
    $0 --source-db 1 --dest-db 2              # Migrate from DB 1 to DB 2
    $0 --dry-run                               # Test without migrating

NOTES:
    - Ensure SSH key authentication is set up for both source and destination servers
    - Both servers should be configured in ~/.ssh/config with their aliases
    - Existing keys in destination database may be overwritten
    - A backup of the destination database will be created before migration
    - All migration files are stored in the backup directory

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--source-server)
                SOURCE_SERVER="$2"
                shift 2
                ;;
            -d|--dest-host)
                DEST_HOST="$2"
                shift 2
                ;;
            -p|--dest-port)
                DEST_PORT="$2"
                shift 2
                ;;
            --source-port)
                SOURCE_PORT="$2"
                shift 2
                ;;
            --source-db)
                SOURCE_DB="$2"
                shift 2
                ;;
            --dest-db)
                DEST_DB="$2"
                shift 2
                ;;
            --backup-dir)
                BACKUP_DIR="$2"
                # Update file paths with new backup directory
                DUMP_FILE="${BACKUP_DIR}/redis_dump_${TIMESTAMP}.txt"
                JSON_DUMP_FILE="${BACKUP_DIR}/redis_dump_${TIMESTAMP}.json"
                LOG_FILE="${BACKUP_DIR}/migration_${TIMESTAMP}.log"
                shift 2
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error_exit "Unknown option: $1. Use -h or --help for usage information."
                ;;
        esac
    done
}

# Main migration function
main() {
    info "Starting Redis migration process..."
    info "Source: ${SOURCE_SERVER}:${SOURCE_PORT} (DB ${SOURCE_DB})"
    info "Destination: ${DEST_HOST}:${DEST_PORT} (DB ${DEST_DB})"

    # Pre-migration checks
    check_dependencies
    setup_backup_dir
    test_source_connection
    test_source_redis
    test_dest_connection
    test_dest_redis
    get_source_info
    get_dest_info

    if [ "${DRY_RUN:-false}" = "true" ]; then
        success "Dry run completed successfully. All checks passed."
        info "To perform actual migration, run without --dry-run flag"
        exit 0
    fi

    # Perform migration
    backup_destination
    dump_source_data
    validate_dump
    import_to_destination
    verify_migration
    generate_report
    cleanup

    success "Redis migration completed successfully!"
    info "Migration files are stored in: ${BACKUP_DIR}"
    info "Check the log file for detailed information: ${LOG_FILE}"
}

# Signal handlers for graceful shutdown
trap 'error_exit "Migration interrupted by user"' INT TERM

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    parse_args "$@"

    # Start migration
    main
fi