{"env": {"es6": true, "node": true, "jest": true}, "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "extends": ["plugin:@typescript-eslint/recommended"], "rules": {"indent": ["error", 2], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "always"], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off"}}