#!/bin/bash

# Health check script for CTR Road Name Translation application
# This script can be used for monitoring and load balancer health checks

set -e

# Configuration
APP_PORT=${1:-3031}
HEALTH_ENDPOINT="http://localhost:$APP_PORT/health"
TIMEOUT=10
MAX_RETRIES=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "[INFO] $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is listening
check_port() {
    if netstat -tuln | grep -q ":$APP_PORT "; then
        log_success "Port $APP_PORT is listening"
        return 0
    else
        log_error "Port $APP_PORT is not listening"
        return 1
    fi
}

# Function to check Redis connection
check_redis() {
    if redis-cli ping > /dev/null 2>&1; then
        log_success "Redis is responding"
        return 0
    else
        log_error "Redis is not responding"
        return 1
    fi
}

# Function to check PM2 status
check_pm2() {
    if pm2 list | grep -q "roadname.*online"; then
        log_success "PM2 application is running"
        return 0
    else
        log_error "PM2 application is not running"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local retry_count=0

    while [ $retry_count -lt $MAX_RETRIES ]; do
        if curl -f -s --max-time $TIMEOUT "$HEALTH_ENDPOINT" > /dev/null 2>&1; then
            log_success "HTTP health endpoint is responding"
            return 0
        elif curl -f -s --max-time $TIMEOUT "http://localhost:$APP_PORT" > /dev/null 2>&1; then
            log_success "HTTP root endpoint is responding"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $MAX_RETRIES ]; then
                log_warning "HTTP endpoint check failed, retrying ($retry_count/$MAX_RETRIES)..."
                sleep 2
            fi
        fi
    done

    log_error "HTTP endpoint is not responding after $MAX_RETRIES attempts"
    return 1
}

# Function to check system resources
check_system_resources() {
    # Check memory usage
    local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        log_warning "High memory usage: ${memory_usage}%"
    else
        log_info "Memory usage: ${memory_usage}%"
    fi

    # Check disk usage
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log_warning "High disk usage: ${disk_usage}%"
    else
        log_info "Disk usage: ${disk_usage}%"
    fi

    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "Load average: $load_avg"
}

# Main health check function
main() {
    log_info "Starting health check for CTR Road Name Translation application..."
    log_info "Checking port: $APP_PORT"

    local exit_code=0

    # Check PM2 status
    if ! check_pm2; then
        exit_code=1
    fi

    # Check if port is listening
    if ! check_port; then
        exit_code=1
    fi

    # Check Redis
    if ! check_redis; then
        exit_code=1
    fi

    # Check HTTP endpoint
    if ! check_http_endpoint; then
        exit_code=1
    fi

    # Check system resources
    check_system_resources

    if [ $exit_code -eq 0 ]; then
        log_success "All health checks passed"
    else
        log_error "Some health checks failed"
    fi

    exit $exit_code
}

# Show usage if help is requested
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Usage: $0 [PORT]"
    echo ""
    echo "Health check script for CTR Road Name Translation application"
    echo ""
    echo "Arguments:"
    echo "  PORT    Application port to check (default: 3031)"
    echo ""
    echo "Exit codes:"
    echo "  0       All checks passed"
    echo "  1       One or more checks failed"
    exit 0
fi

# Run main function
main