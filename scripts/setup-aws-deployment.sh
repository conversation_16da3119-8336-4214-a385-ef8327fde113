#!/bin/bash

# Quick setup script for AWS deployment
# This script helps set up the initial configuration for AWS deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    local missing_tools=()

    if ! command_exists terraform; then
        missing_tools+=("terraform")
    fi

    if ! command_exists aws; then
        missing_tools+=("aws-cli")
    fi

    if ! command_exists ssh; then
        missing_tools+=("ssh")
    fi

    if ! command_exists node; then
        missing_tools+=("node.js")
    fi

    if ! command_exists yarn; then
        missing_tools+=("yarn")
    fi

    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and run this script again."
        exit 1
    fi

    log_success "All prerequisites are installed"
}

# Function to check AWS configuration
check_aws_config() {
    log_info "Checking AWS configuration..."

    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        log_error "AWS CLI is not configured or credentials are invalid"
        log_info "Please run 'aws configure' to set up your credentials"
        exit 1
    fi

    local aws_region=$(aws configure get region)
    if [ -z "$aws_region" ]; then
        log_warning "AWS region is not set. Using default: ap-southeast-1"
        aws configure set region ap-southeast-1
    fi

    log_success "AWS configuration is valid"
}

# Function to generate SSH key
generate_ssh_key() {
    local key_path="$HOME/.ssh/ctr-road-name-key"

    if [ -f "$key_path" ]; then
        log_info "SSH key already exists at $key_path"
        return
    fi

    log_info "Generating SSH key pair..."
    ssh-keygen -t rsa -b 4096 -f "$key_path" -N "" -C "ctr-road-name-deployment"
    chmod 600 "$key_path"
    chmod 644 "$key_path.pub"

    log_success "SSH key generated at $key_path"
}

# Function to create terraform.tfvars
create_terraform_vars() {
    local tfvars_path="terraform/terraform.tfvars"

    if [ -f "$tfvars_path" ]; then
        log_info "terraform.tfvars already exists"
        return
    fi

    log_info "Creating terraform.tfvars..."

    # Get user's public IP
    local public_ip=$(curl -s https://ipinfo.io/ip 2>/dev/null || echo "0.0.0.0")

    # Get public key content
    local public_key_content=$(cat "$HOME/.ssh/ctr-road-name-key.pub")

    # Get AWS region
    local aws_region=$(aws configure get region)

    cat > "$tfvars_path" << EOF
# AWS Configuration
aws_region = "$aws_region"

# Project Configuration
project_name = "ctr-road-name"
environment  = "production"

# Network Configuration
vpc_cidr           = "10.0.0.0/16"
public_subnet_cidr = "********/24"

# EC2 Configuration
instance_type      = "t3a.small"
root_volume_size   = 20
node_version       = "18"
app_port           = 3031

# Security Configuration
allowed_ssh_cidr_blocks = ["$public_ip/32"]
allowed_app_cidr_blocks = ["0.0.0.0/0"]

# SSH Key Configuration
create_key_pair    = true
public_key_content = "$public_key_content"
EOF

    log_success "terraform.tfvars created with your configuration"
    log_warning "Please review and modify terraform/terraform.tfvars as needed"
}

# Function to initialize Terraform
init_terraform() {
    log_info "Initializing Terraform..."

    cd terraform
    terraform init
    cd ..

    log_success "Terraform initialized"
}

# Function to show next steps
show_next_steps() {
    log_info "Setup completed! Next steps:"
    echo ""
    echo "1. Review and modify terraform/terraform.tfvars if needed"
    echo "2. Deploy infrastructure:"
    echo "   cd terraform && terraform plan && terraform apply"
    echo ""
    echo "3. Wait for instance initialization (5-10 minutes)"
    echo ""
    echo "4. Deploy application:"
    echo "   ./deploy_aws.sh --host INSTANCE_IP --key ~/.ssh/ctr-road-name-key --env aws-production"
    echo ""
    echo "5. Test deployment:"
    echo "   curl http://INSTANCE_IP:3031"
    echo ""
    log_info "For detailed instructions, see AWS_DEPLOYMENT_README.md"
}

# Main function
main() {
    log_info "=== CTR Road Name Translation AWS Setup ==="

    check_prerequisites
    check_aws_config
    generate_ssh_key
    create_terraform_vars
    init_terraform
    show_next_steps

    log_success "Setup completed successfully!"
}

# Show usage if help is requested
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Usage: $0"
    echo ""
    echo "Quick setup script for AWS deployment of CTR Road Name Translation"
    echo ""
    echo "This script will:"
    echo "  - Check prerequisites"
    echo "  - Verify AWS configuration"
    echo "  - Generate SSH key pair"
    echo "  - Create terraform.tfvars"
    echo "  - Initialize Terraform"
    echo ""
    echo "Prerequisites:"
    echo "  - Terraform (>= 1.0)"
    echo "  - AWS CLI (>= 2.0)"
    echo "  - Node.js (>= 18)"
    echo "  - Yarn"
    echo "  - SSH client"
    exit 0
fi

# Run main function
main