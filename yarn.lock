# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"2-thenable@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/2-thenable/-/2-thenable-1.0.0.tgz"
  integrity sha512-HqiDzaLDFCXkcCO/SwoyhRwqYtINFHF7t9BDRq4x90TOKNAJpiqUt9X5lQ08bwxYzc067HUywDjGySpebHcUpw==
  dependencies:
    d "1"
    es5-ext "^0.10.47"

"@aws-crypto/crc32@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-2.0.0.tgz"
  integrity sha512-TvE1r2CUueyXOuHdEigYjIZVesInd9KN+K/TFFNfkkxRThiNxO6i4ZqqAVMoEjAamZZ1AA8WXJkjCz7YShHPQA==
  dependencies:
    "@aws-crypto/util" "^2.0.0"
    "@aws-sdk/types" "^3.1.0"
    tslib "^1.11.1"

"@aws-crypto/crc32c@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/crc32c/-/crc32c-2.0.0.tgz"
  integrity sha512-vF0eMdMHx3O3MoOXUfBZry8Y4ZDtcuskjjKgJz8YfIDjLStxTZrYXk+kZqtl6A0uCmmiN/Eb/JbC/CndTV1MHg==
  dependencies:
    "@aws-crypto/util" "^2.0.0"
    "@aws-sdk/types" "^3.1.0"
    tslib "^1.11.1"

"@aws-crypto/ie11-detection@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/ie11-detection/-/ie11-detection-2.0.0.tgz"
  integrity sha512-pkVXf/dq6PITJ0jzYZ69VhL8VFOFoPZLZqtU/12SGnzYuJOOGNfF41q9GxdI1yqC8R13Rq3jOLKDFpUJFT5eTA==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/sha1-browser@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/sha1-browser/-/sha1-browser-2.0.0.tgz"
  integrity sha512-3fIVRjPFY8EG5HWXR+ZJZMdWNRpwbxGzJ9IH9q93FpbgCH8u8GHRi46mZXp3cYD7gealmyqpm3ThZwLKJjWJhA==
  dependencies:
    "@aws-crypto/ie11-detection" "^2.0.0"
    "@aws-crypto/supports-web-crypto" "^2.0.0"
    "@aws-sdk/types" "^3.1.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-browser@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-2.0.0.tgz"
  integrity sha512-rYXOQ8BFOaqMEHJrLHul/25ckWH6GTJtdLSajhlqGMx0PmSueAuvboCuZCTqEKlxR8CQOwRarxYMZZSYlhRA1A==
  dependencies:
    "@aws-crypto/ie11-detection" "^2.0.0"
    "@aws-crypto/sha256-js" "^2.0.0"
    "@aws-crypto/supports-web-crypto" "^2.0.0"
    "@aws-crypto/util" "^2.0.0"
    "@aws-sdk/types" "^3.1.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-js@2.0.0", "@aws-crypto/sha256-js@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-2.0.0.tgz"
  integrity sha512-VZY+mCY4Nmrs5WGfitmNqXzaE873fcIZDu54cbaDaaamsaTOP1DBImV9F4pICc3EHjQXujyE8jig+PFCaew9ig==
  dependencies:
    "@aws-crypto/util" "^2.0.0"
    "@aws-sdk/types" "^3.1.0"
    tslib "^1.11.1"

"@aws-crypto/supports-web-crypto@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-2.0.0.tgz"
  integrity sha512-Ge7WQ3E0OC7FHYprsZV3h0QIcpdyJLvIeg+uTuHqRYm8D6qCFJoiC+edSzSyFiHtZf+NOQDJ1q46qxjtzIY2nA==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/util@^2.0.0":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@aws-crypto/util/-/util-2.0.1.tgz"
  integrity sha512-JJmFFwvbm08lULw4Nm5QOLg8+lAQeC8aCXK5xrtxntYzYXCGfHwUJ4Is3770Q7HmICsXthGQ+ZsDL7C2uH3yBQ==
  dependencies:
    "@aws-sdk/types" "^3.1.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-sdk/abort-controller@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/abort-controller/-/abort-controller-3.110.0.tgz"
  integrity sha512-zok/WEVuK7Jh6V9YeA56pNZtxUASon9LTkS7vE65A4UFmNkPGNBCNgoiBcbhWfxwrZ8wtXcQk6rtUut39831mA==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/chunked-blob-reader-native@3.109.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/chunked-blob-reader-native/-/chunked-blob-reader-native-3.109.0.tgz"
  integrity sha512-Ybn3vDZ3CqGyprL2qdF6QZqoqlx8lA3qOJepobjuKKDRw+KgGxjUY4NvWe0R2MdRoduyaDj6uvhIay0S1MOSJQ==
  dependencies:
    "@aws-sdk/util-base64-browser" "3.109.0"
    tslib "^2.3.1"

"@aws-sdk/chunked-blob-reader@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/chunked-blob-reader/-/chunked-blob-reader-3.55.0.tgz"
  integrity sha512-o/xjMCq81opAjSBjt7YdHJwIJcGVG5XIV9+C2KXcY5QwVimkOKPybWTv0mXPvSwSilSx+EhpLNhkcJuXdzhw4w==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/client-s3@^3.0.0":
  version "3.121.0"
  resolved "https://registry.npmjs.org/@aws-sdk/client-s3/-/client-s3-3.121.0.tgz"
  integrity sha512-iJDqFCRNZM6+iF4E3zSCXKLDrDFxon8gzM0sK8TCkSSwa8Fhk/M/5OKslP9eKfJ1mzmh27IgFDoNnD5P59LbSQ==
  dependencies:
    "@aws-crypto/sha1-browser" "2.0.0"
    "@aws-crypto/sha256-browser" "2.0.0"
    "@aws-crypto/sha256-js" "2.0.0"
    "@aws-sdk/client-sts" "3.121.0"
    "@aws-sdk/config-resolver" "3.110.0"
    "@aws-sdk/credential-provider-node" "3.121.0"
    "@aws-sdk/eventstream-serde-browser" "3.120.0"
    "@aws-sdk/eventstream-serde-config-resolver" "3.110.0"
    "@aws-sdk/eventstream-serde-node" "3.120.0"
    "@aws-sdk/fetch-http-handler" "3.110.0"
    "@aws-sdk/hash-blob-browser" "3.110.0"
    "@aws-sdk/hash-node" "3.110.0"
    "@aws-sdk/hash-stream-node" "3.110.0"
    "@aws-sdk/invalid-dependency" "3.110.0"
    "@aws-sdk/md5-js" "3.110.0"
    "@aws-sdk/middleware-bucket-endpoint" "3.110.0"
    "@aws-sdk/middleware-content-length" "3.110.0"
    "@aws-sdk/middleware-expect-continue" "3.113.0"
    "@aws-sdk/middleware-flexible-checksums" "3.110.0"
    "@aws-sdk/middleware-host-header" "3.110.0"
    "@aws-sdk/middleware-location-constraint" "3.110.0"
    "@aws-sdk/middleware-logger" "3.110.0"
    "@aws-sdk/middleware-recursion-detection" "3.110.0"
    "@aws-sdk/middleware-retry" "3.118.1"
    "@aws-sdk/middleware-sdk-s3" "3.110.0"
    "@aws-sdk/middleware-serde" "3.110.0"
    "@aws-sdk/middleware-signing" "3.110.0"
    "@aws-sdk/middleware-ssec" "3.110.0"
    "@aws-sdk/middleware-stack" "3.110.0"
    "@aws-sdk/middleware-user-agent" "3.110.0"
    "@aws-sdk/node-config-provider" "3.110.0"
    "@aws-sdk/node-http-handler" "3.118.1"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/signature-v4-multi-region" "3.118.0"
    "@aws-sdk/smithy-client" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/url-parser" "3.110.0"
    "@aws-sdk/util-base64-browser" "3.109.0"
    "@aws-sdk/util-base64-node" "3.55.0"
    "@aws-sdk/util-body-length-browser" "3.55.0"
    "@aws-sdk/util-body-length-node" "3.55.0"
    "@aws-sdk/util-defaults-mode-browser" "3.110.0"
    "@aws-sdk/util-defaults-mode-node" "3.110.0"
    "@aws-sdk/util-stream-browser" "3.110.0"
    "@aws-sdk/util-stream-node" "3.110.0"
    "@aws-sdk/util-user-agent-browser" "3.110.0"
    "@aws-sdk/util-user-agent-node" "3.118.0"
    "@aws-sdk/util-utf8-browser" "3.109.0"
    "@aws-sdk/util-utf8-node" "3.109.0"
    "@aws-sdk/util-waiter" "3.118.1"
    "@aws-sdk/xml-builder" "3.109.0"
    entities "2.2.0"
    fast-xml-parser "3.19.0"
    tslib "^2.3.1"

"@aws-sdk/client-sso@3.121.0":
  version "3.121.0"
  resolved "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.121.0.tgz"
  integrity sha512-uYkeUdNnEla57g4QZT0Cu5ll+m0fUQJPkoTXQI5QKeLH2usVpmrCRbtTWEVTh94Gf2x/HK8Ifu7eO/0PquwwIQ==
  dependencies:
    "@aws-crypto/sha256-browser" "2.0.0"
    "@aws-crypto/sha256-js" "2.0.0"
    "@aws-sdk/config-resolver" "3.110.0"
    "@aws-sdk/fetch-http-handler" "3.110.0"
    "@aws-sdk/hash-node" "3.110.0"
    "@aws-sdk/invalid-dependency" "3.110.0"
    "@aws-sdk/middleware-content-length" "3.110.0"
    "@aws-sdk/middleware-host-header" "3.110.0"
    "@aws-sdk/middleware-logger" "3.110.0"
    "@aws-sdk/middleware-recursion-detection" "3.110.0"
    "@aws-sdk/middleware-retry" "3.118.1"
    "@aws-sdk/middleware-serde" "3.110.0"
    "@aws-sdk/middleware-stack" "3.110.0"
    "@aws-sdk/middleware-user-agent" "3.110.0"
    "@aws-sdk/node-config-provider" "3.110.0"
    "@aws-sdk/node-http-handler" "3.118.1"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/smithy-client" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/url-parser" "3.110.0"
    "@aws-sdk/util-base64-browser" "3.109.0"
    "@aws-sdk/util-base64-node" "3.55.0"
    "@aws-sdk/util-body-length-browser" "3.55.0"
    "@aws-sdk/util-body-length-node" "3.55.0"
    "@aws-sdk/util-defaults-mode-browser" "3.110.0"
    "@aws-sdk/util-defaults-mode-node" "3.110.0"
    "@aws-sdk/util-user-agent-browser" "3.110.0"
    "@aws-sdk/util-user-agent-node" "3.118.0"
    "@aws-sdk/util-utf8-browser" "3.109.0"
    "@aws-sdk/util-utf8-node" "3.109.0"
    tslib "^2.3.1"

"@aws-sdk/client-sts@3.121.0":
  version "3.121.0"
  resolved "https://registry.npmjs.org/@aws-sdk/client-sts/-/client-sts-3.121.0.tgz"
  integrity sha512-ZqEcxfeYVeSo/VyXSI4XW4MsWYoRmEdxRLWwI7kgFQxgqwVtfhPmvcaw6CA1atMcSR6waiRSpe9pgpj6gKJvyw==
  dependencies:
    "@aws-crypto/sha256-browser" "2.0.0"
    "@aws-crypto/sha256-js" "2.0.0"
    "@aws-sdk/config-resolver" "3.110.0"
    "@aws-sdk/credential-provider-node" "3.121.0"
    "@aws-sdk/fetch-http-handler" "3.110.0"
    "@aws-sdk/hash-node" "3.110.0"
    "@aws-sdk/invalid-dependency" "3.110.0"
    "@aws-sdk/middleware-content-length" "3.110.0"
    "@aws-sdk/middleware-host-header" "3.110.0"
    "@aws-sdk/middleware-logger" "3.110.0"
    "@aws-sdk/middleware-recursion-detection" "3.110.0"
    "@aws-sdk/middleware-retry" "3.118.1"
    "@aws-sdk/middleware-sdk-sts" "3.110.0"
    "@aws-sdk/middleware-serde" "3.110.0"
    "@aws-sdk/middleware-signing" "3.110.0"
    "@aws-sdk/middleware-stack" "3.110.0"
    "@aws-sdk/middleware-user-agent" "3.110.0"
    "@aws-sdk/node-config-provider" "3.110.0"
    "@aws-sdk/node-http-handler" "3.118.1"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/smithy-client" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/url-parser" "3.110.0"
    "@aws-sdk/util-base64-browser" "3.109.0"
    "@aws-sdk/util-base64-node" "3.55.0"
    "@aws-sdk/util-body-length-browser" "3.55.0"
    "@aws-sdk/util-body-length-node" "3.55.0"
    "@aws-sdk/util-defaults-mode-browser" "3.110.0"
    "@aws-sdk/util-defaults-mode-node" "3.110.0"
    "@aws-sdk/util-user-agent-browser" "3.110.0"
    "@aws-sdk/util-user-agent-node" "3.118.0"
    "@aws-sdk/util-utf8-browser" "3.109.0"
    "@aws-sdk/util-utf8-node" "3.109.0"
    entities "2.2.0"
    fast-xml-parser "3.19.0"
    tslib "^2.3.1"

"@aws-sdk/config-resolver@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/config-resolver/-/config-resolver-3.110.0.tgz"
  integrity sha512-7VvtKy4CL63BAktQ2vgsjhWDSXpkXO5YdiI56LQnHztrvSuJBBaxJ7R1p/k0b2tEUhYKUziAIW8EKE/7EGPR4g==
  dependencies:
    "@aws-sdk/signature-v4" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-config-provider" "3.109.0"
    "@aws-sdk/util-middleware" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-env@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.110.0.tgz"
  integrity sha512-oFU3IYk/Bl5tdsz1qigtm3I25a9cvXPqlE8VjYjxVDdLujF5zd/4HLbhP4GQWhpEwZmM1ijcSNfLcyywVevTZg==
  dependencies:
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-imds@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-imds/-/credential-provider-imds-3.110.0.tgz"
  integrity sha512-atl+7/dAB+8fG9XI2fYyCgXKYDbOzot65VAwis+14bOEUCVp7PCJifBEZ/L8GEq564p+Fa2p1IpV0wuQXxqFUQ==
  dependencies:
    "@aws-sdk/node-config-provider" "3.110.0"
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/url-parser" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-ini@3.121.0":
  version "3.121.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.121.0.tgz"
  integrity sha512-wOuGOifwZtTN/prCaG+hO9AtpKjJB/QyRse251+I+inNPg2iSd9rCLfHZdmfL/Zn2XJyfg0ULOl6c/myF5aRDg==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.110.0"
    "@aws-sdk/credential-provider-imds" "3.110.0"
    "@aws-sdk/credential-provider-sso" "3.121.0"
    "@aws-sdk/credential-provider-web-identity" "3.110.0"
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/shared-ini-file-loader" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-node@3.121.0":
  version "3.121.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.121.0.tgz"
  integrity sha512-wY5+oey0eoxkGMTXrZ+tK7FKA91WN8ntBbYBbZL0vktHYCQkBra5fBGV17RNp8ggVkJXAtDdrIjTBEQ/vNrMrQ==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.110.0"
    "@aws-sdk/credential-provider-imds" "3.110.0"
    "@aws-sdk/credential-provider-ini" "3.121.0"
    "@aws-sdk/credential-provider-process" "3.110.0"
    "@aws-sdk/credential-provider-sso" "3.121.0"
    "@aws-sdk/credential-provider-web-identity" "3.110.0"
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/shared-ini-file-loader" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-process@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.110.0.tgz"
  integrity sha512-JJcZePvRTfQHYj/+EEY13yItnZH/e8exlARFUjN0L13UrgHpOJtDQBa+YBHXo6MbTFQh+re25z2kzc+zOYSMNQ==
  dependencies:
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/shared-ini-file-loader" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-sso@3.121.0":
  version "3.121.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.121.0.tgz"
  integrity sha512-c9XmnndZmJdkSBgDpVQCN8fcVTkRrtDWNUBO6TcA0abxGOOteUS7s9YmJKqMuwABzk+WGJ1B2EVC5b0AMzIFYg==
  dependencies:
    "@aws-sdk/client-sso" "3.121.0"
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/shared-ini-file-loader" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/credential-provider-web-identity@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.110.0.tgz"
  integrity sha512-e4e5u7v3fsUFZsMcFMhMy1NdJBQpunYcLwpYlszm3OEICwTTekQ+hVvnVRd134doHvzepE4yp9sAop0Cj+IRVQ==
  dependencies:
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/eventstream-codec@3.119.0":
  version "3.119.0"
  resolved "https://registry.npmjs.org/@aws-sdk/eventstream-codec/-/eventstream-codec-3.119.0.tgz"
  integrity sha512-HMHVfsYU2yaJ2NMHe1HUhQnWD3hCabC3xTVcAx5SSAE+afc74xoQTCA6oDI6OoCLL47ISLjcrkpvfYCAZ7wHTw==
  dependencies:
    "@aws-crypto/crc32" "2.0.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-hex-encoding" "3.109.0"
    tslib "^2.3.1"

"@aws-sdk/eventstream-serde-browser@3.120.0":
  version "3.120.0"
  resolved "https://registry.npmjs.org/@aws-sdk/eventstream-serde-browser/-/eventstream-serde-browser-3.120.0.tgz"
  integrity sha512-+UUq5vey2mJx1NYhq4Gg/jzs5EJE6gW2g91NPcO842891YxZAOmHciFI5kzLKn8PgSKKhbmCL6pq8UqX4N8lRw==
  dependencies:
    "@aws-sdk/eventstream-serde-universal" "3.120.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/eventstream-serde-config-resolver@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-3.110.0.tgz"
  integrity sha512-0kyKUU5/46OGe6rgIqbNRJEQhNYwxLdgcJXlBl6q6CdgyQApz6jsAgG0C5xhSLSi4iJijDRriJTowAhkq4AlWQ==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/eventstream-serde-node@3.120.0":
  version "3.120.0"
  resolved "https://registry.npmjs.org/@aws-sdk/eventstream-serde-node/-/eventstream-serde-node-3.120.0.tgz"
  integrity sha512-+RoUQKzB+MBH6nThLmc/VnmwNMzWxiCD8Z8KbGUG+1ybYqshSwGKObCqDfAIwe+W97xNZpsx4Br7/bcPEY322g==
  dependencies:
    "@aws-sdk/eventstream-serde-universal" "3.120.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/eventstream-serde-universal@3.120.0":
  version "3.120.0"
  resolved "https://registry.npmjs.org/@aws-sdk/eventstream-serde-universal/-/eventstream-serde-universal-3.120.0.tgz"
  integrity sha512-2tZ5+3YlQRfsd0xibgVueWegengOMZIZF3ksq+IygWrRwukI9+QfC7oYe29/yttKoz2AipNKNY+JL9MgjHEdmg==
  dependencies:
    "@aws-sdk/eventstream-codec" "3.119.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/fetch-http-handler@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/fetch-http-handler/-/fetch-http-handler-3.110.0.tgz"
  integrity sha512-vk+K4GeCZL2J2rtvKO+T0Q7i3MDpEGZBMg5K2tj9sMcEQwty0BF0aFnP7Eu2l4/Zif2z1mWuUFM2WcZI6DVnbw==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/querystring-builder" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-base64-browser" "3.109.0"
    tslib "^2.3.1"

"@aws-sdk/hash-blob-browser@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/hash-blob-browser/-/hash-blob-browser-3.110.0.tgz"
  integrity sha512-NkTosjlYwP2dcBXY6yzhNafAK+W2nceheffvWdyGA29+E9YdRjDminXvKc/WAkZUMOW0CaCbD90otOiimAAYyQ==
  dependencies:
    "@aws-sdk/chunked-blob-reader" "3.55.0"
    "@aws-sdk/chunked-blob-reader-native" "3.109.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/hash-node@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/hash-node/-/hash-node-3.110.0.tgz"
  integrity sha512-wakl+kP2O8wTGYiQ3InZy+CVfGrIpFfq9fo4zif9PZac0BbUbguUU1dkY34uZiaf+4o2/9MoDYrHU2HYeXKxWw==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-buffer-from" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/hash-stream-node@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/hash-stream-node/-/hash-stream-node-3.110.0.tgz"
  integrity sha512-srlStn+dCnBlQy4oWBz3oFS8vT5Xgxhra91rt9U+vHruCyQ0L1es0J87X4uwy2HRlnIw3daPtVLtxekahEXzKQ==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/invalid-dependency@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/invalid-dependency/-/invalid-dependency-3.110.0.tgz"
  integrity sha512-O8J1InmtJkoiUMbQDtxBfOzgigBp9iSVsNXQrhs2qHh3826cJOfE7NGT3u+NMw73Pk5j2cfmOh1+7k/76IqxOg==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/is-array-buffer@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/is-array-buffer/-/is-array-buffer-3.55.0.tgz"
  integrity sha512-NbiPHVYuPxdqdFd6FxzzN3H1BQn/iWA3ri3Ry7AyLeP/tGs1yzEWMwf8BN8TSMALI0GXT6Sh0GDWy3Ok5xB6DA==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/md5-js@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/md5-js/-/md5-js-3.110.0.tgz"
  integrity sha512-66gV6CH8O7ymTZMIbGjdUI71K7ErDfudhtN/ULb97kD2TYX4NlFtxNZxx3+iZH1G0H636lWm9hJcU5ELG9B+bw==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-utf8-browser" "3.109.0"
    "@aws-sdk/util-utf8-node" "3.109.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-bucket-endpoint@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.110.0.tgz"
  integrity sha512-l1q0KzMRFyGSSc7LZGEh2xhCha1933C8uJE5g23b7dZdklEU5I62l4daELo+TBANcxFzDiRXd6g5mly/T+ZTSg==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-arn-parser" "3.55.0"
    "@aws-sdk/util-config-provider" "3.109.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-content-length@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-content-length/-/middleware-content-length-3.110.0.tgz"
  integrity sha512-hKU+zdqfAJQg22LXMVu/z35nNIHrVAKpVKPe9+WYVdL/Z7JKUPK7QymqKGOyDuDbzW6OxyulC1zKGEX12zGmdA==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-expect-continue@3.113.0":
  version "3.113.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.113.0.tgz"
  integrity sha512-LLtSunCYVWeAhRP+6enn0kYF119WooV6gepMGOWeRCpKXO2iyi8YOx2Mtgc3T8ybiAG/dVlmZoX47Y1HINcuqg==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-flexible-checksums@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.110.0.tgz"
  integrity sha512-Z/v1Da+e1McxrVr1s4jUykp2EXsOHpTxZ4M0X8vNkXCIVSuaMp4UI0P+LQawbDA+j3FaecqqBfWMZ2sHQ8bpoA==
  dependencies:
    "@aws-crypto/crc32" "2.0.0"
    "@aws-crypto/crc32c" "2.0.0"
    "@aws-sdk/is-array-buffer" "3.55.0"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-host-header@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.110.0.tgz"
  integrity sha512-/Cknn1vL2LTlclI0MX2RzmtdPlCJ5palCRXxm/mod1oHwg4oNTKRlUX3LUD+L8g7JuJ4h053Ch9KS/A0vanE5Q==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-location-constraint@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.110.0.tgz"
  integrity sha512-8ZSo9sqrTMcSp0xEJQ3ypmQpeSMQl1NXXv72khJPweZqDoO0eAbfytwyH4JH4sP0VwVVmuDHdwPXyDZX7I0iQg==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-logger@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.110.0.tgz"
  integrity sha512-+pz+a+8dfTnzLj79nHrv3aONMp/N36/erMd+7JXeR84QEosVLrFBUwKA8x5x6O3s1iBbQzRKMYEIuja9xn1BPA==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-recursion-detection@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.110.0.tgz"
  integrity sha512-Wav782zd7bcd1e6txRob76CDOdVOaUQ8HXoywiIm/uFrEEUZvhs2mgnXjVUVCMBUehdNgnL99z420aS13JeL/Q==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-retry@3.118.1":
  version "3.118.1"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-retry/-/middleware-retry-3.118.1.tgz"
  integrity sha512-Dh0EgO3yPHEaRC6CVrofgAMdUQaG0Kkl466iVFHN5n5kExQvCtvpMHwO9N7kqaq9lXten3yhzboRNLIo98E1Kw==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/service-error-classification" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-middleware" "3.110.0"
    tslib "^2.3.1"
    uuid "^8.3.2"

"@aws-sdk/middleware-sdk-s3@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.110.0.tgz"
  integrity sha512-/PpZU11dkGldD6yeAccPxFd5nzofLOA3+j25RdIwz2jlJMLl9TeznYRtFH5JhHonP3lsK+IPEnFPwuL6gkBxIQ==
  dependencies:
    "@aws-sdk/middleware-bucket-endpoint" "3.110.0"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-arn-parser" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-sdk-sts@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-sdk-sts/-/middleware-sdk-sts-3.110.0.tgz"
  integrity sha512-EjY/YFdlr5jECde6qIrTIyGBbn/34CKcQGKvmvRd31+3qaClIJLAwNuHfcVzWvCUGbAslsfvdbOpLju33pSQRA==
  dependencies:
    "@aws-sdk/middleware-signing" "3.110.0"
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/signature-v4" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-serde@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-serde/-/middleware-serde-3.110.0.tgz"
  integrity sha512-brVupxgEAmcZ9cZvdHEH8zncjvGKIiud8pOe4fiimp5NpHmjBLew4jUbnOKNZNAjaidcKUtz//cxtutD6yXEww==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-signing@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-signing/-/middleware-signing-3.110.0.tgz"
  integrity sha512-y6ZKrGYfgDlFMzWhZmoq5J1UctBgZOUvMmnU9sSeZ020IlEPiOxFMvR0Zu6TcYThp8uy3P0wyjQtGYeTl9Z/kA==
  dependencies:
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/signature-v4" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-ssec@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-ssec/-/middleware-ssec-3.110.0.tgz"
  integrity sha512-Zrm+h+C+MXv2Q+mh8O/zwK2hUYM4kq4I1vx72RPpvyfIk4/F5ZzeA3LSVluISyAW+iNqS8XFvGFrzl2gB8zWsg==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/middleware-stack@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-stack/-/middleware-stack-3.110.0.tgz"
  integrity sha512-iaLHw6ctOuGa9UxNueU01Xes+15dR+mqioRpUOUZ9Zx+vhXVpD7C8lnNqhRnYeFXs10/rNIzASgsIrAHTlnlIQ==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/middleware-user-agent@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.110.0.tgz"
  integrity sha512-Y6FgiZr99DilYq6AjeaaWcNwVlSQpNGKrILzvV4Tmz03OaBIspe4KL+8EZ2YA/sAu5Lpw80vItdezqDOwGAlnQ==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/node-config-provider@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/node-config-provider/-/node-config-provider-3.110.0.tgz"
  integrity sha512-46p4dCPGYctuybTQTwLpjenA1QFHeyJw/OyggGbtUJUy+833+ldnAwcPVML2aXJKUKv3APGI8vq1kaloyNku3Q==
  dependencies:
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/shared-ini-file-loader" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/node-http-handler@3.118.1":
  version "3.118.1"
  resolved "https://registry.npmjs.org/@aws-sdk/node-http-handler/-/node-http-handler-3.118.1.tgz"
  integrity sha512-pfWVAUNJEs0UW0KkDqq2/VCz9PIpvg4mYEfCVZ4jR+Rv8F7UezNeM3FrEdHk8dfYShH+OV0hFskHBQJhw1BX2Q==
  dependencies:
    "@aws-sdk/abort-controller" "3.110.0"
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/querystring-builder" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/property-provider@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/property-provider/-/property-provider-3.110.0.tgz"
  integrity sha512-7NkpmYeOkK3mhWBNU+/zSDqwzeaSPH1qrq4L//WV7WS/weYyE/jusQeZoOxVsuZQnQEXHt5O2hKVeUwShl12xA==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/protocol-http@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/protocol-http/-/protocol-http-3.110.0.tgz"
  integrity sha512-qdi2gCbJiyPyLn+afebPNp/5nVCRh1X7t7IRIFl3FHVEC+o54u/ojay/MLZ4M/+X9Fa4Zxsb0Wpp3T0xAHVDBg==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/querystring-builder@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/querystring-builder/-/querystring-builder-3.110.0.tgz"
  integrity sha512-7V3CDXj519izmbBn9ZE68ymASwGriA+Aq+cb/yHSVtffnvXjPtvONNw7G/5iVblisGLSCUe2hSvpYtcaXozbHw==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-uri-escape" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/querystring-parser@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/querystring-parser/-/querystring-parser-3.110.0.tgz"
  integrity sha512-//pJHH7hrhdDMZGBPKXKymmC/tJM7gFT0w/qbu/yd3Wm4W2fMB+8gkmj6EZctx7jrsWlfRQuvFejKqEfapur/g==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/service-error-classification@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/service-error-classification/-/service-error-classification-3.110.0.tgz"
  integrity sha512-ccgCE0pU/4RmXR6CP3fLAdhPAve7bK/yXBbGzpSHGAQOXqNxYzOsAvQ30Jg6X+qjLHsI/HR2pLIE65z4k6tynw==

"@aws-sdk/shared-ini-file-loader@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/shared-ini-file-loader/-/shared-ini-file-loader-3.110.0.tgz"
  integrity sha512-E1ERoqEoG206XNBYWCKLgHkzCbTxdpDEGbsLET2DnvjFsT0s9p2dPvVux3bYl7JVAhyGduE+qcqWk7MzhFCBNQ==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/signature-v4-multi-region@3.118.0":
  version "3.118.0"
  resolved "https://registry.npmjs.org/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.118.0.tgz"
  integrity sha512-Uih3SR5d3XBeUtiMFkDERx7jLOZSXvVrhikA9p416FIPWZ5649sQ/esYsDvWBB39nbnYMx/QlsR+imCvh8XlhQ==
  dependencies:
    "@aws-sdk/protocol-http" "3.110.0"
    "@aws-sdk/signature-v4" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-arn-parser" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/signature-v4@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/signature-v4/-/signature-v4-3.110.0.tgz"
  integrity sha512-utxxdllOnmQDhbpipnFAbuQ4c2pwefZ+2hi48jKvQRULQ2PO4nxLmdZm6B0FXaTijbKsyO7GrMik+EZ6mi3ARQ==
  dependencies:
    "@aws-sdk/is-array-buffer" "3.55.0"
    "@aws-sdk/types" "3.110.0"
    "@aws-sdk/util-hex-encoding" "3.109.0"
    "@aws-sdk/util-middleware" "3.110.0"
    "@aws-sdk/util-uri-escape" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/smithy-client@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/smithy-client/-/smithy-client-3.110.0.tgz"
  integrity sha512-gNLYrmdAe/1hVF2Nv2LF4OkL1A0a1o708pEMZHzql9xP164omRDaLrGDhz9tH7tsJEgLz+Bf4E8nTuISeDwvGg==
  dependencies:
    "@aws-sdk/middleware-stack" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/types@3.110.0", "@aws-sdk/types@^3.1.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/types/-/types-3.110.0.tgz"
  integrity sha512-dLVoqODU3laaqNFPyN1QLtlQnwX4gNPMXptEBIt/iJpuZf66IYJe6WCzVZGt4Zfa1CnUmrlA428AzdcA/KCr2A==

"@aws-sdk/url-parser@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/url-parser/-/url-parser-3.110.0.tgz"
  integrity sha512-tILFB8/Q73yzgO0dErJNnELmmBszd0E6FucwAnG3hfDefjqCBe09Q/1yhu2aARXyRmZa4AKp0sWcdwIWHc8dnA==
  dependencies:
    "@aws-sdk/querystring-parser" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/util-arn-parser@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-arn-parser/-/util-arn-parser-3.55.0.tgz"
  integrity sha512-76KJxp4MRWufHYWys7DFl64znr5yeJ3AIQNAPCKKw1sP0hzO7p6Kx0PaJnw9x+CPSzOrT4NbuApL6/srYhKDGg==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-base64-browser@3.109.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-base64-browser/-/util-base64-browser-3.109.0.tgz"
  integrity sha512-lAZ6fyDGiRLaIsKT9qh7P9FGuNyZ4gAbr1YOSQk/5mHtaTuUvxlPptZuInNM/0MPQm6lpcot00D8IWTucn4PbA==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-base64-node@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-base64-node/-/util-base64-node-3.55.0.tgz"
  integrity sha512-UQ/ZuNoAc8CFMpSiRYmevaTsuRKzLwulZTnM8LNlIt9Wx1tpNvqp80cfvVj7yySKROtEi20wq29h31dZf1eYNQ==
  dependencies:
    "@aws-sdk/util-buffer-from" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/util-body-length-browser@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-body-length-browser/-/util-body-length-browser-3.55.0.tgz"
  integrity sha512-Ei2OCzXQw5N6ZkTMZbamUzc1z+z1R1Ja5tMEagz5BxuX4vWdBObT+uGlSzL8yvTbjoPjnxWA2aXyEqaUP3JS8Q==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-body-length-node@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-body-length-node/-/util-body-length-node-3.55.0.tgz"
  integrity sha512-lU1d4I+9wJwydduXs0SxSfd+mHKjxeyd39VwOv6i2KSwWkPbji9UQqpflKLKw+r45jL7+xU/zfeTUg5Tt/3Gew==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-buffer-from@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-buffer-from/-/util-buffer-from-3.55.0.tgz"
  integrity sha512-uVzKG1UgvnV7XX2FPTylBujYMKBPBaq/qFBxfl0LVNfrty7YjpfieQxAe6yRLD+T0Kir/WDQwGvYC+tOYG3IGA==
  dependencies:
    "@aws-sdk/is-array-buffer" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/util-config-provider@3.109.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-config-provider/-/util-config-provider-3.109.0.tgz"
  integrity sha512-GrAZl/aBv0A28LkyNyq8SPJ5fmViCwz80fWLMeWx/6q5AbivuILogjlWwEZSvZ9zrlHOcFC0+AnCa5pQrjaslw==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-defaults-mode-browser@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-defaults-mode-browser/-/util-defaults-mode-browser-3.110.0.tgz"
  integrity sha512-Y2dcOOD20S3bv/IjUqpdKIiDt6995SXNG5Pu/LeSdXNyLCOIm9rX4gHTxl9fC1KK5M/gR9fGJ362f67WwqEEqw==
  dependencies:
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    bowser "^2.11.0"
    tslib "^2.3.1"

"@aws-sdk/util-defaults-mode-node@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-defaults-mode-node/-/util-defaults-mode-node-3.110.0.tgz"
  integrity sha512-Cr3Z5nyrw1KowjbW76xp8hkT/zJtYjAVZ9PS4l84KxIicbVvDOBpxG3yNddkuQcavmlH6G4wH9uM5DcnpKDncg==
  dependencies:
    "@aws-sdk/config-resolver" "3.110.0"
    "@aws-sdk/credential-provider-imds" "3.110.0"
    "@aws-sdk/node-config-provider" "3.110.0"
    "@aws-sdk/property-provider" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/util-hex-encoding@3.109.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-hex-encoding/-/util-hex-encoding-3.109.0.tgz"
  integrity sha512-s8CgTNrn3cLkrdiohfxLuOYPCanzvHn/aH5RW6DaMoeQiG5Hl9QUiP/WtdQ9QQx3xvpQFpmvxIaSBwSgFNLQxA==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.55.0.tgz"
  integrity sha512-0sPmK2JaJE2BbTcnvybzob/VrFKCXKfN4CUKcvn0yGg/me7Bz+vtzQRB3Xp+YSx+7OtWxzv63wsvHoAnXvgxgg==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-middleware@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-middleware/-/util-middleware-3.110.0.tgz"
  integrity sha512-PTVWrI5fA9d5hHJs6RzX2dIS2jRQ3uW073Fm0BePpQeDdZrEk+S5KNwRhUtpN6sdSV45vm6S9rrjZUG51qwGmA==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-stream-browser@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-stream-browser/-/util-stream-browser-3.110.0.tgz"
  integrity sha512-kAMrHtgrhr6ODRnzt/V+LSDVDvejcbdUp19n4My2vrPwKw3lM65vT+FAPIlGeDQBtOOhmlTbrYM3G3KKnlnHyg==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/util-stream-node@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-stream-node/-/util-stream-node-3.110.0.tgz"
  integrity sha512-jgkO7aLRpE3EUqU5XUdo0FmlyBVCFHKyHd/jdEN8h9+XMa44rl2QMdOSFQtwaNI4NC8J+OC66u2dQ+8QQnOLig==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/util-uri-escape@3.55.0":
  version "3.55.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-uri-escape/-/util-uri-escape-3.55.0.tgz"
  integrity sha512-mmdDLUpFCN2nkfwlLdOM54lTD528GiGSPN1qb8XtGLgZsJUmg3uJSFIN2lPeSbEwJB3NFjVas/rnQC48i7mV8w==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-user-agent-browser@3.110.0":
  version "3.110.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.110.0.tgz"
  integrity sha512-rNdhmHDMV5dNJctqlBWimkZLJRB+x03DB+61pm+SKSFk6gPIVIvc1WNXqDFphkiswT4vA13ZUkGHzt+N4+noQQ==
  dependencies:
    "@aws-sdk/types" "3.110.0"
    bowser "^2.11.0"
    tslib "^2.3.1"

"@aws-sdk/util-user-agent-node@3.118.0":
  version "3.118.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.118.0.tgz"
  integrity sha512-7j21HNumxMkeUpgoMX8o3y66k+qMSEkCPCMGnoiiMtgfWX9SY0S/fLwR1nDBw8HI3NthRXfgWdAXUu8K3Kjp6g==
  dependencies:
    "@aws-sdk/node-config-provider" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/util-utf8-browser@3.109.0", "@aws-sdk/util-utf8-browser@^3.0.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.109.0.tgz"
  integrity sha512-FmcGSz0v7Bqpl1SE8G1Gc0CtDpug+rvqNCG/szn86JApD/f5x8oByjbEiAyTU2ZH2VevUntx6EW68ulHyH+x+w==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-utf8-node@3.109.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-utf8-node/-/util-utf8-node-3.109.0.tgz"
  integrity sha512-Ti/ZBdvz2eSTElsucjzNmzpyg2MwfD1rXmxD0hZuIF8bPON/0+sZYnWd5CbDw9kgmhy28dmKue086tbZ1G0iLQ==
  dependencies:
    "@aws-sdk/util-buffer-from" "3.55.0"
    tslib "^2.3.1"

"@aws-sdk/util-waiter@3.118.1":
  version "3.118.1"
  resolved "https://registry.npmjs.org/@aws-sdk/util-waiter/-/util-waiter-3.118.1.tgz"
  integrity sha512-mCPTpoNHXdBcGEk/8r90ppCB/DHUis+dZPDBDfCENRqcAYq9TDlTl9VB7jhgRkVUhM0HZGNAbUOaI+212jjPiQ==
  dependencies:
    "@aws-sdk/abort-controller" "3.110.0"
    "@aws-sdk/types" "3.110.0"
    tslib "^2.3.1"

"@aws-sdk/xml-builder@3.109.0":
  version "3.109.0"
  resolved "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.109.0.tgz"
  integrity sha512-+aAXynnrqya1Eukz4Gxch4xIXCZolIMWGD4Ll/Q5yXT5uAjGh2HQWd9J0LWE+gYChpWetZbAVYZ3cEJ6F+SpZA==
  dependencies:
    tslib "^2.3.1"

"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz"
  integrity sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@dabh/diagnostics@^2.0.2":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@dabh/diagnostics/-/diagnostics-2.0.3.tgz"
  integrity sha512-hrlQOIi7hAfzsMqlGSFyVucrx38O+j6wiGOf//H2ecvIEqYN4ADBSS2iLMh5UFyDunCNniUIPk/q3riFv45xRA==
  dependencies:
    colorspace "1.1.x"
    enabled "2.0.x"
    kuler "^2.0.0"

"@discoveryjs/json-ext@^0.5.0":
  version "0.5.7"
  resolved "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz"
  integrity sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==

"@feathersjs/adapter-commons@^4.5.11":
  version "4.5.12"
  resolved "https://registry.npmjs.org/@feathersjs/adapter-commons/-/adapter-commons-4.5.12.tgz"
  integrity sha512-5UTo3M0WARIAUSOWFrHuf668pVewuQyOBuZXvvOa76SKe6/n31HulKFlXI2+FmBascOhaJibVBsP7jfcoQ6HfQ==
  dependencies:
    "@feathersjs/commons" "^4.5.12"
    "@feathersjs/errors" "^4.5.12"
    "@feathersjs/feathers" "^4.5.12"

"@feathersjs/authentication-local@^4.5.12":
  version "4.5.12"
  resolved "https://registry.npmjs.org/@feathersjs/authentication-local/-/authentication-local-4.5.12.tgz"
  integrity sha512-4L2sfJjxfdBgcZ9EoH7E5JMsOzxK50rwdhs8ijbJ5+9bV2ixoCz/oyclypUiN21smmIMorODXWgOeMN7qU27og==
  dependencies:
    "@feathersjs/authentication" "^4.5.12"
    "@feathersjs/errors" "^4.5.12"
    "@feathersjs/feathers" "^4.5.12"
    bcryptjs "^2.4.3"
    debug "^4.3.3"
    lodash "^4.17.21"

"@feathersjs/authentication-oauth@^4.5.12":
  version "4.5.12"
  resolved "https://registry.npmjs.org/@feathersjs/authentication-oauth/-/authentication-oauth-4.5.12.tgz"
  integrity sha512-mhK9KPRAXTVQ9p+6tSSj1+K0aquApYmf/AXN6IN5PEbpGOwIxTloWbQf+lKyHrzpGw/btxKNVAFbV4kTzsmKSQ==
  dependencies:
    "@feathersjs/authentication" "^4.5.12"
    "@feathersjs/errors" "^4.5.12"
    "@feathersjs/express" "^4.5.12"
    "@feathersjs/feathers" "^4.5.12"
    debug "^4.3.3"
    express-session "^1.17.2"
    grant "^4.7.0"
    grant-profile "^0.0.11"
    lodash "^4.17.21"

"@feathersjs/authentication@^4.5.12":
  version "4.5.12"
  resolved "https://registry.npmjs.org/@feathersjs/authentication/-/authentication-4.5.12.tgz"
  integrity sha512-qNLSlSZ0lCM77ELf7Ae+mkgXE1HafwWU2q/2MbSQfva1zUTo34O59JHa3PKmDK+TqAQwYQVlAY9IR6SmK+MW1g==
  dependencies:
    "@feathersjs/errors" "^4.5.12"
    "@feathersjs/feathers" "^4.5.12"
    "@feathersjs/transport-commons" "^4.5.12"
    "@types/jsonwebtoken" "^8.5.6"
    debug "^4.3.3"
    jsonwebtoken "^8.5.1"
    lodash "^4.17.21"
    long-timeout "^0.1.1"
    uuid "^8.3.2"

"@feathersjs/commons@^4.5.11", "@feathersjs/commons@^4.5.12", "@feathersjs/commons@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmjs.org/@feathersjs/commons/-/commons-4.5.15.tgz"
  integrity sha512-eVEcLJL3GcPQBQcD77jI3fZ4MMx+iD02Px2Z1lDw+cn/iGCeQkWWAPQs4Tp+HGR1rTL5JO+unyhzN8V9X7OYmw==

"@feathersjs/configuration@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmjs.org/@feathersjs/configuration/-/configuration-4.5.15.tgz"
  integrity sha512-Uj8/wZoWDOQC0gBitXHSQfmsF7WBFBSzOIlFPnJlrC9+L43fDnkkTRXWDZ5k2t7yFFHlnHOX7wl/TUSg/ZLrXg==
  dependencies:
    "@feathersjs/feathers" "^4.5.15"
    config "^3.3.6"
    debug "^4.3.3"

"@feathersjs/errors@^4.5.11", "@feathersjs/errors@^4.5.12", "@feathersjs/errors@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmjs.org/@feathersjs/errors/-/errors-4.5.15.tgz"
  integrity sha512-EJAYWMsWZlUA/halbgZBc6eP2d3HPkHi5GzprYEK58GCh51KJ36VjX7VKVEnMjgBSED+PE00tpr3ZvUC4nUR+Q==
  dependencies:
    debug "^4.3.3"

"@feathersjs/express@^4.5.12":
  version "4.5.14"
  resolved "https://registry.npmjs.org/@feathersjs/express/-/express-4.5.14.tgz"
  integrity sha512-kVMRiilBw767K1rYBOdLXYZGzwJb3pzKiOygUtSaZe6FsUmjJTycUz6pM7/ij1bI6WhzmSW0O4cfzmfoEXdejg==
  dependencies:
    "@feathersjs/commons" "^4.5.12"
    "@feathersjs/errors" "^4.5.12"
    "@types/express" "^4.17.13"
    debug "^4.3.3"
    express "^4.17.2"
    lodash "^4.17.21"
    uberproto "^2.0.6"

"@feathersjs/feathers@^4.5.12", "@feathersjs/feathers@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmjs.org/@feathersjs/feathers/-/feathers-4.5.15.tgz"
  integrity sha512-Q7sa1QAEhspYHsVEJPRg313bqjNaOhgvibiUxtjf3CGGJL9hVcAPLHQbrzsZuVTR4QwBATZSf6voEtuNmG/pIQ==
  dependencies:
    "@feathersjs/commons" "^4.5.15"
    debug "^4.3.3"
    events "^3.3.0"
    uberproto "^2.0.6"

"@feathersjs/socketio@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmjs.org/@feathersjs/socketio/-/socketio-4.5.15.tgz"
  integrity sha512-ma3mKeD7XC5su9pXWqbtauvDJgCXDms1tGZkY2mKET+P52zuqzhUyksPE1RLFu/Rpy28U77Uu39pAM9342hfDg==
  dependencies:
    "@feathersjs/transport-commons" "^4.5.15"
    "@types/socket.io" "^2.1.11"
    debug "^4.3.3"
    socket.io "^2.3.0"
    uberproto "^2.0.6"

"@feathersjs/transport-commons@^4.5.12", "@feathersjs/transport-commons@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmjs.org/@feathersjs/transport-commons/-/transport-commons-4.5.15.tgz"
  integrity sha512-i9VqWq4VEASqvF+00kwIKhfvCU3tarLuywGMeN4zM3HXuuJKGIv6g0DAaZyWvgqhiuopZmMtHoP8mFRYIPxCEg==
  dependencies:
    "@feathersjs/commons" "^4.5.15"
    "@feathersjs/errors" "^4.5.15"
    debug "^4.3.3"
    lodash "^4.17.21"
    radix-router "^3.0.1"

"@gar/promisify@^1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@gar/promisify/-/promisify-1.1.3.tgz"
  integrity sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==

"@isaacs/string-locale-compare@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@isaacs/string-locale-compare/-/string-locale-compare-1.1.0.tgz"
  integrity sha512-SQ7Kzhh9+D+ZW9MA0zkYv3VXhIDNx+LzM6EJ+/65I3QY+enU6Itte7E5XX7EWrqLW2FN4n06GWzBnPoC3th2aQ==

"@jridgewell/gen-mapping@^0.3.0":
  version "0.3.2"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  integrity sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.0.3":
  version "3.0.7"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.7.tgz"
  integrity sha512-8cXDaBBHOr2pQ7j77Y6Vp5VDT2sIqWyWQ56TjEq4ih/a4iST3dItRe8Q9fp0rrIl9DoKhWQtUQz/YpOxLkXbNA==

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==

"@jridgewell/source-map@^0.3.2":
  version "0.3.2"
  resolved "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.2.tgz"
  integrity sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.13"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.13.tgz"
  integrity sha512-GryiOJmNcWbovBxTfZSF71V/mXbgcV3MewDe3kIMCLyIh5e7SKAeUZs+rMnJ8jkMolZ/4/VsdBmMrw3l+VdZ3w==

"@jridgewell/trace-mapping@0.3.9", "@jridgewell/trace-mapping@^0.3.7", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.9"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@kwsites/file-exists@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@kwsites/file-exists/-/file-exists-1.1.1.tgz"
  integrity sha512-m9/5YGR18lIwxSFDwfE3oA7bWuq9kdau6ugN4H2rJeyhFQZcG9AgSHkQtSD15a8WvTgfz9aikZMrKPHvbpqFiw==
  dependencies:
    debug "^4.1.1"

"@kwsites/promise-deferred@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@kwsites/promise-deferred/-/promise-deferred-1.1.1.tgz"
  integrity sha512-GaHYm+c0O9MjZRu0ongGBRbinu8gVAMd2UZjji6jVmqKtZluZnptXGWhz1E8j8D2HJ3f/yMxKAUC0b+57wncIw==

"@mole-inc/bin-wrapper@^8.0.1":
  version "8.0.1"
  resolved "https://registry.yarnpkg.com/@mole-inc/bin-wrapper/-/bin-wrapper-8.0.1.tgz#d7fd0ceb1cfa8a855293a3ed9d7d135f4d442f0e"
  integrity sha512-sTGoeZnjI8N4KS+sW2AN95gDBErhAguvkw/tWdCjeM8bvxpz5lqrnd0vOJABA1A+Ic3zED7PYoLP/RANLgVotA==
  dependencies:
    bin-check "^4.1.0"
    bin-version-check "^5.0.0"
    content-disposition "^0.5.4"
    ext-name "^5.0.0"
    file-type "^17.1.6"
    filenamify "^5.0.2"
    got "^11.8.5"
    os-filter-obj "^2.0.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@npmcli/arborist@^5.0.0", "@npmcli/arborist@^5.0.4":
  version "5.4.0"
  resolved "https://registry.yarnpkg.com/@npmcli/arborist/-/arborist-5.4.0.tgz#beef01e0b47c682b74cae4c9266f5720bf1cdf0a"
  integrity sha512-gWDDQjoRndukgV9DLDXLqgzY2sIbUJ0D7JNgNlLuMFbei4Gm7EWYulpOyIjYxdYXM9b0L3sAniOOlOVMkMNMXA==
  dependencies:
    "@isaacs/string-locale-compare" "^1.1.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    "@npmcli/map-workspaces" "^2.0.3"
    "@npmcli/metavuln-calculator" "^3.0.1"
    "@npmcli/move-file" "^2.0.0"
    "@npmcli/name-from-folder" "^1.0.1"
    "@npmcli/node-gyp" "^2.0.0"
    "@npmcli/package-json" "^2.0.0"
    "@npmcli/query" "^1.1.1"
    "@npmcli/run-script" "^4.1.3"
    bin-links "^3.0.0"
    cacache "^16.0.6"
    common-ancestor-path "^1.0.1"
    json-parse-even-better-errors "^2.3.1"
    json-stringify-nice "^1.1.4"
    minimatch "^5.1.0"
    mkdirp "^1.0.4"
    mkdirp-infer-owner "^2.0.0"
    nopt "^5.0.0"
    npm-install-checks "^5.0.0"
    npm-package-arg "^9.0.0"
    npm-pick-manifest "^7.0.0"
    npm-registry-fetch "^13.0.0"
    npmlog "^6.0.2"
    pacote "^13.6.1"
    parse-conflict-json "^2.0.1"
    proc-log "^2.0.0"
    promise-all-reject-late "^1.0.0"
    promise-call-limit "^1.0.1"
    read-package-json-fast "^2.0.2"
    readdir-scoped-modules "^1.1.0"
    rimraf "^3.0.2"
    semver "^7.3.7"
    ssri "^9.0.0"
    treeverse "^2.0.0"
    walk-up-path "^1.0.0"

"@npmcli/ci-detect@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@npmcli/ci-detect/-/ci-detect-2.0.0.tgz"
  integrity sha512-8yQtQ9ArHh/TzdUDKQwEvwCgpDuhSWTDAbiKMl3854PcT+Dk4UmWaiawuFTLy9n5twzXOBXVflWe+90/ffXQrA==

"@npmcli/config@^4.1.0":
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/@npmcli/config/-/config-4.2.0.tgz#62b5d2b9cbf93fb2bc9f7cc947f25d7659ef849f"
  integrity sha512-imWNz5dNWb2u+y41jyxL2WB389tkhu3a01Rchn16O/ur6GrnKySgOqdNG3N/9Z+mqxdISMEGKXI/POCauzz0dA==
  dependencies:
    "@npmcli/map-workspaces" "^2.0.2"
    ini "^3.0.0"
    mkdirp-infer-owner "^2.0.0"
    nopt "^5.0.0"
    proc-log "^2.0.0"
    read-package-json-fast "^2.0.3"
    semver "^7.3.5"
    walk-up-path "^1.0.0"

"@npmcli/disparity-colors@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@npmcli/disparity-colors/-/disparity-colors-2.0.0.tgz"
  integrity sha512-FFXGrIjhvd2qSZ8iS0yDvbI7nbjdyT2VNO7wotosjYZM2p2r8PN3B7Om3M5NO9KqW/OVzfzLB3L0V5Vo5QXC7A==
  dependencies:
    ansi-styles "^4.3.0"

"@npmcli/fs@^2.1.0":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@npmcli/fs/-/fs-2.1.1.tgz#c0c480b03450d8b9fc086816a50cb682668a48bf"
  integrity sha512-1Q0uzx6c/NVNGszePbr5Gc2riSU1zLpNlo/1YWntH+eaPmMgBssAW0qXofCVkpdj3ce4swZtlDYQu+NKiYcptg==
  dependencies:
    "@gar/promisify" "^1.1.3"
    semver "^7.3.5"

"@npmcli/git@^3.0.0":
  version "3.0.1"
  resolved "https://registry.npmjs.org/@npmcli/git/-/git-3.0.1.tgz"
  integrity sha512-UU85F/T+F1oVn3IsB/L6k9zXIMpXBuUBE25QDH0SsURwT6IOBqkC7M16uqo2vVZIyji3X1K4XH9luip7YekH1A==
  dependencies:
    "@npmcli/promise-spawn" "^3.0.0"
    lru-cache "^7.4.4"
    mkdirp "^1.0.4"
    npm-pick-manifest "^7.0.0"
    proc-log "^2.0.0"
    promise-inflight "^1.0.1"
    promise-retry "^2.0.1"
    semver "^7.3.5"
    which "^2.0.2"

"@npmcli/installed-package-contents@^1.0.7":
  version "1.0.7"
  resolved "https://registry.npmjs.org/@npmcli/installed-package-contents/-/installed-package-contents-1.0.7.tgz"
  integrity sha512-9rufe0wnJusCQoLpV9ZPKIVP55itrM5BxOXs10DmdbRfgWtHy1LDyskbwRnBghuB0PrF7pNPOqREVtpz4HqzKw==
  dependencies:
    npm-bundled "^1.1.1"
    npm-normalize-package-bin "^1.0.1"

"@npmcli/map-workspaces@^2.0.2", "@npmcli/map-workspaces@^2.0.3":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-2.0.3.tgz"
  integrity sha512-X6suAun5QyupNM8iHkNPh0AHdRC2rb1W+MTdMvvA/2ixgmqZwlq5cGUBgmKHUHT2LgrkKJMAXbfAoTxOigpK8Q==
  dependencies:
    "@npmcli/name-from-folder" "^1.0.1"
    glob "^8.0.1"
    minimatch "^5.0.1"
    read-package-json-fast "^2.0.3"

"@npmcli/metavuln-calculator@^3.0.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-3.1.1.tgz"
  integrity sha512-n69ygIaqAedecLeVH3KnO39M6ZHiJ2dEv5A7DGvcqCB8q17BGUgW8QaanIkbWUo2aYGZqJaOORTLAlIvKjNDKA==
  dependencies:
    cacache "^16.0.0"
    json-parse-even-better-errors "^2.3.1"
    pacote "^13.0.3"
    semver "^7.3.5"

"@npmcli/move-file@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@npmcli/move-file/-/move-file-2.0.0.tgz"
  integrity sha512-UR6D5f4KEGWJV6BGPH3Qb2EtgH+t+1XQ1Tt85c7qicN6cezzuHPdZwwAxqZr4JLtnQu0LZsTza/5gmNmSl8XLg==
  dependencies:
    mkdirp "^1.0.4"
    rimraf "^3.0.2"

"@npmcli/name-from-folder@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@npmcli/name-from-folder/-/name-from-folder-1.0.1.tgz"
  integrity sha512-qq3oEfcLFwNfEYOQ8HLimRGKlD8WSeGEdtUa7hmzpR8Sa7haL1KVQrvgO6wqMjhWFFVjgtrh1gIxDz+P8sjUaA==

"@npmcli/node-gyp@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@npmcli/node-gyp/-/node-gyp-2.0.0.tgz"
  integrity sha512-doNI35wIe3bBaEgrlPfdJPaCpUR89pJWep4Hq3aRdh6gKazIVWfs0jHttvSSoq47ZXgC7h73kDsUl8AoIQUB+A==

"@npmcli/package-json@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@npmcli/package-json/-/package-json-2.0.0.tgz"
  integrity sha512-42jnZ6yl16GzjWSH7vtrmWyJDGVa/LXPdpN2rcUWolFjc9ON2N3uz0qdBbQACfmhuJZ2lbKYtmK5qx68ZPLHMA==
  dependencies:
    json-parse-even-better-errors "^2.3.1"

"@npmcli/promise-spawn@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@npmcli/promise-spawn/-/promise-spawn-3.0.0.tgz"
  integrity sha512-s9SgS+p3a9Eohe68cSI3fi+hpcZUmXq5P7w0kMlAsWVtR7XbK3ptkZqKT2cK1zLDObJ3sR+8P59sJE0w/KTL1g==
  dependencies:
    infer-owner "^1.0.4"

"@npmcli/query@^1.1.1":
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/@npmcli/query/-/query-1.1.1.tgz#462c4268473ae39e89d5fefbad94d9af7e1217c4"
  integrity sha512-UF3I0fD94wzQ84vojMO2jDB8ibjRSTqhi8oz2mzVKiJ9gZHbeGlu9kzPvgHuGDK0Hf2cARhWtTfCDHNEwlL9hg==
  dependencies:
    npm-package-arg "^9.1.0"
    postcss-selector-parser "^6.0.10"
    semver "^7.3.7"

"@npmcli/run-script@^4.1.0", "@npmcli/run-script@^4.1.3", "@npmcli/run-script@^4.1.5":
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/@npmcli/run-script/-/run-script-4.2.0.tgz#2c25758f80831ba138afe25225d456e89acedac3"
  integrity sha512-e/QgLg7j2wSJp1/7JRl0GC8c7PMX+uYlA/1Tb+IDOLdSM4T7K1VQ9mm9IGU3WRtY5vEIObpqCLb3aCNCug18DA==
  dependencies:
    "@npmcli/node-gyp" "^2.0.0"
    "@npmcli/promise-spawn" "^3.0.0"
    node-gyp "^9.0.0"
    read-package-json-fast "^2.0.3"
    which "^2.0.2"

"@redis/bloom@1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@redis/bloom/-/bloom-1.0.2.tgz"
  integrity sha512-EBw7Ag1hPgFzdznK2PBblc1kdlj5B5Cw3XwI9/oG7tSn85/HKy3X9xHy/8tm/eNXJYHLXHJL/pkwBpFMVVefkw==

"@redis/client@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@redis/client/-/client-1.1.0.tgz"
  integrity sha512-xO9JDIgzsZYDl3EvFhl6LC52DP3q3GCMUer8zHgKV6qSYsq1zB+pZs9+T80VgcRogrlRYhi4ZlfX6A+bHiBAgA==
  dependencies:
    cluster-key-slot "1.1.0"
    generic-pool "3.8.2"
    yallist "4.0.0"

"@redis/graph@1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@redis/graph/-/graph-1.0.1.tgz"
  integrity sha512-oDE4myMCJOCVKYMygEMWuriBgqlS5FqdWerikMoJxzmmTUErnTRRgmIDa2VcgytACZMFqpAOWDzops4DOlnkfQ==

"@redis/json@1.0.3":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@redis/json/-/json-1.0.3.tgz"
  integrity sha512-4X0Qv0BzD9Zlb0edkUoau5c1bInWSICqXAGrpwEltkncUwcxJIGEcVryZhLgb0p/3PkKaLIWkjhHRtLe9yiA7Q==

"@redis/search@1.0.6":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@redis/search/-/search-1.0.6.tgz"
  integrity sha512-pP+ZQRis5P21SD6fjyCeLcQdps+LuTzp2wdUbzxEmNhleighDDTD5ck8+cYof+WLec4csZX7ks+BuoMw0RaZrA==

"@redis/time-series@1.0.3":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@redis/time-series/-/time-series-1.0.3.tgz"
  integrity sha512-OFp0q4SGrTH0Mruf6oFsHGea58u8vS/iI5+NpYdicaM+7BgqBZH8FFvNZ8rYYLrUO/QRqMq72NpXmxLVNcdmjA==

"@serverless/dashboard-plugin@^6.2.2":
  version "6.2.2"
  resolved "https://registry.npmjs.org/@serverless/dashboard-plugin/-/dashboard-plugin-6.2.2.tgz"
  integrity sha512-h3zOprpuWZCdAP7qoOKT2nboB+AaxMkGoSzOD0jIBpt9s0cXqLE2VFjR2vKn8Cvam47Qa3XYnT2/XN6tR6rZgQ==
  dependencies:
    "@serverless/event-mocks" "^1.1.1"
    "@serverless/platform-client" "^4.3.2"
    "@serverless/utils" "^6.0.3"
    child-process-ext "^2.1.1"
    chokidar "^3.5.3"
    flat "^5.0.2"
    fs-extra "^9.1.0"
    js-yaml "^4.1.0"
    jszip "^3.9.1"
    lodash "^4.17.21"
    memoizee "^0.4.15"
    ncjsm "^4.3.0"
    node-dir "^0.1.17"
    node-fetch "^2.6.7"
    open "^7.4.2"
    semver "^7.3.7"
    simple-git "^3.7.0"
    type "^2.6.0"
    uuid "^8.3.2"
    yamljs "^0.3.0"

"@serverless/event-mocks@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@serverless/event-mocks/-/event-mocks-1.1.1.tgz"
  integrity sha512-YAV5V/y+XIOfd+HEVeXfPWZb8C6QLruFk9tBivoX2roQLWVq145s4uxf8D0QioCueuRzkukHUS4JIj+KVoS34A==
  dependencies:
    "@types/lodash" "^4.14.123"
    lodash "^4.17.11"

"@serverless/platform-client@^4.3.2":
  version "4.3.2"
  resolved "https://registry.npmjs.org/@serverless/platform-client/-/platform-client-4.3.2.tgz"
  integrity sha512-DAa5Z0JAZc6UfrTZLYwqoZxgAponZpFwaqd7WzzMA+loMCkYWyJNwxrAmV6cr2UUJpkko4toPZuJ3vM9Ie+NDA==
  dependencies:
    adm-zip "^0.5.5"
    archiver "^5.3.0"
    axios "^0.21.1"
    fast-glob "^3.2.7"
    https-proxy-agent "^5.0.0"
    ignore "^5.1.8"
    isomorphic-ws "^4.0.1"
    js-yaml "^3.14.1"
    jwt-decode "^2.2.0"
    minimatch "^3.0.4"
    querystring "^0.2.1"
    run-parallel-limit "^1.1.0"
    throat "^5.0.0"
    traverse "^0.6.6"
    ws "^7.5.3"

"@serverless/utils@^6.0.3", "@serverless/utils@^6.6.0":
  version "6.6.0"
  resolved "https://registry.npmjs.org/@serverless/utils/-/utils-6.6.0.tgz"
  integrity sha512-+zw5m41L44psgKh9Snj0tVaXKI2mg/MW2l7VlySjAEK5jqLKHNmFMw0n2oD75nbaJvr2xYhc05wmeFdLqVF6Sw==
  dependencies:
    archive-type "^4.0.0"
    chalk "^4.1.2"
    ci-info "^3.3.1"
    cli-progress-footer "^2.3.1"
    content-disposition "^0.5.4"
    d "^1.0.1"
    decompress "^4.2.1"
    event-emitter "^0.3.5"
    ext "^1.6.0"
    ext-name "^5.0.0"
    file-type "^16.5.3"
    filenamify "^4.3.0"
    get-stream "^6.0.1"
    got "^11.8.3"
    inquirer "^8.2.4"
    js-yaml "^4.1.0"
    jwt-decode "^3.1.2"
    lodash "^4.17.21"
    log "^6.3.1"
    log-node "^8.0.3"
    make-dir "^3.1.0"
    memoizee "^0.4.15"
    ncjsm "^4.3.0"
    node-fetch "^2.6.7"
    open "^7.4.2"
    p-event "^4.2.0"
    supports-color "^8.1.1"
    timers-ext "^0.1.7"
    type "^2.6.0"
    uni-global "^1.0.0"
    uuid "^8.3.2"
    write-file-atomic "^4.0.1"

"@sindresorhus/is@^0.14.0":
  version "0.14.0"
  resolved "https://registry.npmjs.org/@sindresorhus/is/-/is-0.14.0.tgz"
  integrity sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==

"@sindresorhus/is@^4.0.0":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@sindresorhus/is/-/is-4.6.0.tgz"
  integrity sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==

"@socket.io/component-emitter@~3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.0.tgz"
  integrity sha512-+9jVqKhRSpsc591z5vX+X5Yyw+he/HCB4iQ/RYxw35CEPaY1gnsNE43nf9n9AaYjAQrTiI/mOwKUKdUs9vf7Xg==

"@swc/cli@^0.1.57":
  version "0.1.65"
  resolved "https://registry.yarnpkg.com/@swc/cli/-/cli-0.1.65.tgz#bb51ce6f088a78ac99a07507c15a8d74c9336ecb"
  integrity sha512-4NcgsvJVHhA7trDnMmkGLLvWMHu2kSy+qHx6QwRhhJhdiYdNUrhdp+ERxen73sYtaeEOYeLJcWrQ60nzKi6rpg==
  dependencies:
    "@mole-inc/bin-wrapper" "^8.0.1"
    commander "^7.1.0"
    fast-glob "^3.2.5"
    minimatch "^9.0.3"
    semver "^7.3.8"
    slash "3.0.0"
    source-map "^0.7.3"

"@swc/core-darwin-arm64@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-darwin-arm64/-/core-darwin-arm64-1.9.1.tgz#0fd83e5febe1044c7b12f128089cb8b213e14d0b"
  integrity sha512-2/ncHSCdAh5OHem1fMITrWEzzl97OdMK1PHc9CkxSJnphLjRubfxB5sbc5tDhcO68a5tVy+DxwaBgDec3PXnOg==

"@swc/core-darwin-x64@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-darwin-x64/-/core-darwin-x64-1.9.1.tgz#da28fcd37207655d2ad34dcb0d0819f20decb57a"
  integrity sha512-4MDOFC5zmNqRJ9RGFOH95oYf27J9HniLVpB1pYm2gGeNHdl2QvDMtx2QTuMHQ6+OTn/3y1BHYuhBGp7d405oLA==

"@swc/core-linux-arm-gnueabihf@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.9.1.tgz#dde1a15d1b88a6be000bbcecebe301227eb76c57"
  integrity sha512-eVW/BjRW8/HpLe3+1jRU7w7PdRLBgnEEYTkHJISU8805/EKT03xNZn6CfaBpKfeAloY4043hbGzE/NP9IahdpQ==

"@swc/core-linux-arm64-gnu@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.9.1.tgz#8b6d15b56597ba5e097932d3a305e88c3d749cec"
  integrity sha512-8m3u1v8R8NgI/9+cHMkzk14w87blSy3OsQPWPfhOL+XPwhyLPvat+ahQJb2nZmltjTgkB4IbzKFSfbuA34LmNA==

"@swc/core-linux-arm64-musl@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.9.1.tgz#dd822efe61b2bbbd378e6ca8d80b4ba992c34ad8"
  integrity sha512-hpT0sQAZnW8l02I289yeyFfT9llGO9PzKDxUq8pocKtioEHiElRqR53juCWoSmzuWi+6KX7zUJ0NKCBrc8pmDg==

"@swc/core-linux-x64-gnu@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.9.1.tgz#d02c63e96d4137c988e71189ccf1c40deb43b4cc"
  integrity sha512-sGFdpdAYusk/ropHiwtXom2JrdaKPxl8MqemRv6dvxZq1Gm/GdmOowxdXIPjCgBGMgoXVcgNviH6CgiO5q+UtA==

"@swc/core-linux-x64-musl@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.9.1.tgz#a8736ddb8e573aa59ccceb50813badff806b299b"
  integrity sha512-YtNLNwIWs0Z2+XgBs6+LrCIGtfCDtNr4S4b6Q5HDOreEIGzSvhkef8eyBI5L+fJ2eGov4b7iEo61C4izDJS5RA==

"@swc/core-win32-arm64-msvc@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.9.1.tgz#91c75fe95cd7bb7f7ae85c6b3bd405af63dc5984"
  integrity sha512-qSxD3uZW2vSiHqUt30vUi0PB92zDh9bjqh5YKpfhhVa7h1vt/xXhlid8yMvSNToTfzhRrTEffOAPUr7WVoyQUA==

"@swc/core-win32-ia32-msvc@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.9.1.tgz#e2ea0be7ca34b642b3adbb6c1fad34fb7874514b"
  integrity sha512-C3fPEwyX/WRPlX6zIToNykJuz1JkZX0sk8H1QH2vpnKuySUkt/Ur5K2FzLgSWzJdbfxstpgS151/es0VGAD+ZA==

"@swc/core-win32-x64-msvc@1.9.1":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.9.1.tgz#c9e532a791fdf44e3c9635135b1026f140d06483"
  integrity sha512-2XZ+U1AyVsOAXeH6WK1syDm7+gwTjA8fShs93WcbxnK7HV+NigDlvr4124CeJLTHyh3fMh1o7+CnQnaBJhlysQ==

"@swc/core@^1.3.0":
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/@swc/core/-/core-1.9.1.tgz#1a1b8378e4b64b74e7f932014ca800ea6133ac27"
  integrity sha512-OnPc+Kt5oy3xTvr/KCUOqE9ptJcWbyQgAUr1ydh9EmbBcmJTaO1kfQCxm/axzJi6sKeDTxL9rX5zvLOhoYIaQw==
  dependencies:
    "@swc/counter" "^0.1.3"
    "@swc/types" "^0.1.14"
  optionalDependencies:
    "@swc/core-darwin-arm64" "1.9.1"
    "@swc/core-darwin-x64" "1.9.1"
    "@swc/core-linux-arm-gnueabihf" "1.9.1"
    "@swc/core-linux-arm64-gnu" "1.9.1"
    "@swc/core-linux-arm64-musl" "1.9.1"
    "@swc/core-linux-x64-gnu" "1.9.1"
    "@swc/core-linux-x64-musl" "1.9.1"
    "@swc/core-win32-arm64-msvc" "1.9.1"
    "@swc/core-win32-ia32-msvc" "1.9.1"
    "@swc/core-win32-x64-msvc" "1.9.1"

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/@swc/counter/-/counter-0.1.3.tgz#cc7463bd02949611c6329596fccd2b0ec782b0e9"
  integrity sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==

"@swc/types@^0.1.14":
  version "0.1.14"
  resolved "https://registry.yarnpkg.com/@swc/types/-/types-0.1.14.tgz#0a0a3f60f801c5d7d52ab02fd5f924d9c6dbcb0d"
  integrity sha512-PbSmTiYCN+GMrvfjrMo9bdY+f2COnwbdnoMw7rqU/PI5jXpKjxOGZ0qqZCImxnT81NkNsKnmEpvu+hRXLBeCJg==
  dependencies:
    "@swc/counter" "^0.1.3"

"@szmarczak/http-timer@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.1.2.tgz"
  integrity sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==
  dependencies:
    defer-to-connect "^1.0.1"

"@szmarczak/http-timer@^4.0.5":
  version "4.0.6"
  resolved "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.6.tgz"
  integrity sha512-4BAffykYOgO+5nzBWYwE3W90sBgLJoUPRWWcL8wlyiM8IB8ipJz3UMJ9KXQd1RKQXpKp8Tutn80HZtWsu2u76w==
  dependencies:
    defer-to-connect "^2.0.0"

"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmjs.org/@tokenizer/token/-/token-0.3.0.tgz"
  integrity sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==

"@tootallnate/once@2":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz"
  integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.9.tgz"
  integrity sha512-jNsYVVxU8v5g43Erja32laIDHXeoNvFEpX33OK4d6hljo3jDhCBDhx5dhCCTMWUojscpAagGiRkBKxpdl9fxqA==

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==

"@tsconfig/node16@^1.0.2":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.3.tgz"
  integrity sha512-yOlFc+7UtL/89t2ZhjPvvB/DeAr3r+Dq58IgzsFkOAvVC6NMJXmCGjbptdXdR9qsX7pKcTL+s87FtYREi2dEEQ==

"@types/body-parser@*":
  version "1.19.2"
  resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz"
  integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cacheable-request@^6.0.1":
  version "6.0.2"
  resolved "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-6.0.2.tgz"
  integrity sha512-B3xVo+dlKM6nnKTcmm5ZtY/OL8bOAOd2Olee9M1zft65ox50OzjEHW91sDiU9j6cvW8Ejg1/Qkf4xd2kugApUA==
  dependencies:
    "@types/http-cache-semantics" "*"
    "@types/keyv" "*"
    "@types/node" "*"
    "@types/responselike" "*"

"@types/compression@^1.7.2":
  version "1.7.2"
  resolved "https://registry.npmjs.org/@types/compression/-/compression-1.7.2.tgz"
  integrity sha512-lwEL4M/uAGWngWFLSG87ZDr2kLrbuR8p7X+QZB1OQlT+qkHsCPDVFnHPyXf4Vyl4yDDorNY+mAhosxkCvppatg==
  dependencies:
    "@types/express" "*"

"@types/connect-redis@^0.0.18":
  version "0.0.18"
  resolved "https://registry.npmjs.org/@types/connect-redis/-/connect-redis-0.0.18.tgz"
  integrity sha512-iGygGbXgPIr94DEAuoluWhzre3c2/ew5NPlbW9IWvwCTXMM1YCmc7M9wpXMkYqt6kB9aO1sjZnmDzyugUu+2vQ==
  dependencies:
    "@types/express" "*"
    "@types/express-session" "*"
    "@types/ioredis" "*"
    "@types/redis" "^2.8.0"

"@types/connect@*":
  version "3.4.35"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz"
  integrity sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==
  dependencies:
    "@types/node" "*"

"@types/cors@^2.8.12":
  version "2.8.12"
  resolved "https://registry.npmjs.org/@types/cors/-/cors-2.8.12.tgz"
  integrity sha512-vt+kDhq/M2ayberEtJcIN/hxXy1Pk+59g2FV/ZQceeaTyCtCucjL2Q7FXlFjtWn4n15KCr1NE2lNNFhp0lEThw==

"@types/cron@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@types/cron/-/cron-2.0.0.tgz"
  integrity sha512-xZM08fqvwIXgghtPVkSPKNgC+JoMQ2OHazEvyTKnNf7aWu1aB6/4lBbQFrb03Td2cUGG7ITzMv3mFYnMu6xRaQ==
  dependencies:
    "@types/luxon" "*"
    "@types/node" "*"

"@types/engine.io@*":
  version "3.1.7"
  resolved "https://registry.npmjs.org/@types/engine.io/-/engine.io-3.1.7.tgz"
  integrity sha512-qNjVXcrp+1sS8YpRUa714r0pgzOwESdW5UjHL7D/2ZFdBX0BXUXtg1LUrp+ylvqbvMcMWUy73YpRoxPN2VoKAQ==
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  version "3.7.3"
  resolved "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.3.tgz"
  integrity sha512-PB3ldyrcnAicT35TWPs5IcwKD8S333HMaa2VVv4+wdvebJkjWuW/xESoB8IwRcog8HYVYamb1g/R31Qv5Bx03g==
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.4.3"
  resolved "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.3.tgz"
  integrity sha512-YP1S7YJRMPs+7KZKDb9G63n8YejIwW9BALq7a5j2+H4yl6iOv9CB29edho+cuFRrvmJbbaH2yiVChKLJVysDGw==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^0.0.51":
  version "0.0.51"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-0.0.51.tgz"
  integrity sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==

"@types/express-serve-static-core@^4.17.18":
  version "4.17.28"
  resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz"
  integrity sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express-session@*", "@types/express-session@^1.17.4":
  version "1.17.4"
  resolved "https://registry.npmjs.org/@types/express-session/-/express-session-1.17.4.tgz"
  integrity sha512-7cNlSI8+oOBUHTfPXMwDxF/Lchx5aJ3ho7+p9jJZYVg9dVDJFh3qdMXmJtRsysnvS+C6x46k9DRYmrmCkE+MVg==
  dependencies:
    "@types/express" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.13"
  resolved "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz"
  integrity sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-cache-semantics@*":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.1.tgz"
  integrity sha512-SZs7ekbP8CN0txVG2xVRH6EgKmEm31BOxA07vkFaETzZz1xh+********************************+iRPQ==

"@types/ioredis@*":
  version "4.28.10"
  resolved "https://registry.npmjs.org/@types/ioredis/-/ioredis-4.28.10.tgz"
  integrity sha512-69LyhUgrXdgcNDv7ogs1qXZomnfOEnSmrmMFqKgt1XMJxmoOSG/u3wYy13yACIfKuMJ8IhKgHafDO3sx19zVQQ==
  dependencies:
    "@types/node" "*"

"@types/jest@^27.0.3":
  version "27.5.2"
  resolved "https://registry.npmjs.org/@types/jest/-/jest-27.5.2.tgz"
  integrity sha512-mpT8LJJ4CMeeahobofYWIjFo0xonRS/HfxnVEPMPFSQdGUt1uHCnoPT7Zhb+sjDU2wz0oKV0OLUR0WzrHNgfeA==
  dependencies:
    jest-matcher-utils "^27.0.0"
    pretty-format "^27.0.0"

"@types/json-buffer@~3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@types/json-buffer/-/json-buffer-3.0.0.tgz"
  integrity sha512-3YP80IxxFJB4b5tYC2SUPwkg0XQLiu0nWvhRgEatgjf+29IcWO9X1k8xRv5DGssJ/lCrjYTjQPcobJr2yWIVuQ==

"@types/json-schema@*", "@types/json-schema@^7.0.8":
  version "7.0.11"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.11.tgz"
  integrity sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==

"@types/jsonwebtoken@^8.5.6", "@types/jsonwebtoken@^8.5.8":
  version "8.5.8"
  resolved "https://registry.npmjs.org/@types/jsonwebtoken/-/jsonwebtoken-8.5.8.tgz"
  integrity sha512-zm6xBQpFDIDM6o9r6HSgDeIcLy82TKWctCXEPbJJcXb5AKmi5BNNdLXneixK4lplX3PqIVcwLBCGE/kAGnlD4A==
  dependencies:
    "@types/node" "*"

"@types/keyv@*":
  version "3.1.4"
  resolved "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.4.tgz"
  integrity sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==
  dependencies:
    "@types/node" "*"

"@types/lodash@^4.14.123":
  version "4.14.182"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.182.tgz"
  integrity sha512-/THyiqyQAP9AfARo4pF+aCGcyiQ94tX/Is2I7HofNRqoYLgN1PBoOWu2/zTA5zMxzP5EFutMtWtGAFRKUe961Q==

"@types/luxon@*":
  version "2.3.2"
  resolved "https://registry.npmjs.org/@types/luxon/-/luxon-2.3.2.tgz"
  integrity sha512-WOehptuhKIXukSUUkRgGbj2c997Uv/iUgYgII8U7XLJqq9W2oF0kQ6frEznRQbdurioz+L/cdaIm4GutTQfgmA==

"@types/milliseconds@^0.0.30":
  version "0.0.30"
  resolved "https://registry.npmjs.org/@types/milliseconds/-/milliseconds-0.0.30.tgz"
  integrity sha512-MVgBCpNG6tuPctUGqaGsbMLm5E4bYy8Ki7G4RMmcMH83e15iMY9L7bJccWpXv3Zyo8y0GQlvUj+yOShWlQlTOA==

"@types/mime@^1":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz"
  integrity sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw==

"@types/morgan@^1.9.9":
  version "1.9.9"
  resolved "https://registry.yarnpkg.com/@types/morgan/-/morgan-1.9.9.tgz#d60dec3979e16c203a000159daa07d3fb7270d7f"
  integrity sha512-iRYSDKVaC6FkGSpEVVIvrRGw0DfJMiQzIn3qr2G5B3C//AWkulhXgaBd7tS9/J79GWSYMTHGs7PfI5b3Y8m+RQ==
  dependencies:
    "@types/node" "*"

"@types/multer-s3@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@types/multer-s3/-/multer-s3-3.0.0.tgz"
  integrity sha512-s8dZjVsLBdaOaCzWjmn6x7WA1LjWgfhCgc2cIK21EI0pgrROFvooAJSrULdD+8qiW51DnYWAY8pOanBe6LLXzg==
  dependencies:
    "@aws-sdk/client-s3" "^3.0.0"
    "@types/multer" "*"

"@types/multer@*":
  version "1.4.7"
  resolved "https://registry.npmjs.org/@types/multer/-/multer-1.4.7.tgz"
  integrity sha512-/SNsDidUFCvqqcWDwxv2feww/yqhNeTRL5CVoL3jU4Goc4kKEL10T7Eye65ZqPNi4HRx8sAEX59pV1aEH7drNA==
  dependencies:
    "@types/express" "*"

"@types/node@*":
  version "17.0.33"
  resolved "https://registry.npmjs.org/@types/node/-/node-17.0.33.tgz"
  integrity sha512-miWq2m2FiQZmaHfdZNcbpp9PuXg34W5JZ5CrJ/BaS70VuhoJENBEQybeiYSaPBRNq6KQGnjfEnc/F3PN++D+XQ==

"@types/qs@*":
  version "6.9.7"
  resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz"
  integrity sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz"
  integrity sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==

"@types/redis@^2.8.0":
  version "2.8.32"
  resolved "https://registry.npmjs.org/@types/redis/-/redis-2.8.32.tgz"
  integrity sha512-7jkMKxcGq9p242exlbsVzuJb57KqHRhNl4dHoQu2Y5v9bCAbtIXXH0R3HleSQW4CTOqpHIYUW3t6tpUj4BVQ+w==
  dependencies:
    "@types/node" "*"

"@types/redis@^4.0.11":
  version "4.0.11"
  resolved "https://registry.npmjs.org/@types/redis/-/redis-4.0.11.tgz"
  integrity sha512-bI+gth8La8Wg/QCR1+V1fhrL9+LZUSWfcqpOj2Kc80ZQ4ffbdL173vQd5wovmoV9i071FU9oP2g6etLuEwb6Rg==
  dependencies:
    redis "*"

"@types/responselike@*", "@types/responselike@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@types/responselike/-/responselike-1.0.0.tgz"
  integrity sha512-85Y2BjiufFzaMIlvJDvTTB8Fxl2xfLo4HgmHzVBz08w4wDePCTjYw66PdrolO0kzli3yam/YCgRufyo1DdQVTA==
  dependencies:
    "@types/node" "*"

"@types/serve-favicon@^2.5.3":
  version "2.5.3"
  resolved "https://registry.npmjs.org/@types/serve-favicon/-/serve-favicon-2.5.3.tgz"
  integrity sha512-HirXLRJjLXzwiSnjhE1vMu55X7+qaY+noXsKqi/7eK1uByl3L6TwkcALZuJnQXqOalMdmBz3b662yXvaR+89Vw==
  dependencies:
    "@types/express" "*"

"@types/serve-static@*":
  version "1.13.10"
  resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz"
  integrity sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/socket.io-parser@*", "@types/socket.io-parser@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@types/socket.io-parser/-/socket.io-parser-3.0.0.tgz"
  integrity sha512-Ry/rbTE6HQNL9eu3LpL1Ocup5VexXu1bSSGlSho/IR5LuRc8YvxwSNJ3JxqTltVJEATLbZkMQETSbxfKNgp4Ew==
  dependencies:
    socket.io-parser "*"

"@types/socket.io@^2.1.11":
  version "2.1.13"
  resolved "https://registry.npmjs.org/@types/socket.io/-/socket.io-2.1.13.tgz"
  integrity sha512-JRgH3nCgsWel4OPANkhH8TelpXvacAJ9VeryjuqCDiaVDMpLysd6sbt0dr6Z15pqH3p2YpOT3T1C5vQ+O/7uyg==
  dependencies:
    "@types/engine.io" "*"
    "@types/node" "*"
    "@types/socket.io-parser" "*"

"@types/strip-bom@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@types/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-xevGOReSYGM7g/kUBZzPqCrR/KYAo+F0yiPc85WFTJa0MSLtyFTVTU6cJu/aV4mid7IffDIWqo69THF2o4JiEQ==

"@types/strip-json-comments@0.0.30":
  version "0.0.30"
  resolved "https://registry.npmjs.org/@types/strip-json-comments/-/strip-json-comments-0.0.30.tgz"
  integrity sha512-7NQmHra/JILCd1QqpSzl8+mJRc8ZHz3uDm8YV1Ks9IhK0epEiTw8aIErbvH9PI+6XbqhyIQy3462nEsn7UVzjQ==

"@types/webidl-conversions@*":
  version "6.1.1"
  resolved "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-6.1.1.tgz"
  integrity sha512-XAahCdThVuCFDQLT7R7Pk/vqeObFNL3YqRyFZg+AqAP/W1/w3xHaIxuW7WszQqTbIBOPRcItYJIou3i/mppu3Q==

"@types/whatwg-url@^8.2.1":
  version "8.2.1"
  resolved "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-8.2.1.tgz"
  integrity sha512-2YubE1sjj5ifxievI5Ge1sckb9k/Er66HyR2c+3+I6VDUUg1TLPdYYTEbQ+DjRkS4nTxMJhgWfSfMRD2sl2EYQ==
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

"@webassemblyjs/ast@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.1.tgz"
  integrity sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.1.tgz"
  integrity sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ==

"@webassemblyjs/helper-api-error@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz"
  integrity sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg==

"@webassemblyjs/helper-buffer@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.1.tgz"
  integrity sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA==

"@webassemblyjs/helper-numbers@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.1.tgz"
  integrity sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.1.tgz"
  integrity sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q==

"@webassemblyjs/helper-wasm-section@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz"
  integrity sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz"
  integrity sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.1.tgz"
  integrity sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.1.tgz"
  integrity sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ==

"@webassemblyjs/wasm-edit@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz"
  integrity sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz"
  integrity sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz"
  integrity sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz"
  integrity sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz"
  integrity sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.2.0.tgz"
  integrity sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==

"@webpack-cli/info@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@webpack-cli/info/-/info-1.5.0.tgz"
  integrity sha512-e8tSXZpw2hPl2uMJY6fsMswaok5FdlGNRTktvFk2sD8RjH0hE2+XistawJx1vmKteh4NmGmNUrp+Tb2w+udPcQ==
  dependencies:
    envinfo "^7.7.3"

"@webpack-cli/serve@^1.7.0":
  version "1.7.0"
  resolved "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.7.0.tgz"
  integrity sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

abbrev@1, abbrev@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.7.6:
  version "1.8.0"
  resolved "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz"
  integrity sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==

acorn-walk@^8.1.1:
  version "8.2.0"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.2.0.tgz"
  integrity sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==

acorn@^8.4.1, acorn@^8.5.0:
  version "8.7.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.7.1.tgz"
  integrity sha512-Xx54uLJQZ19lKygFXOWsscKUbsBZW0CPykPhVQdhIeIwrbPmJzqeASDInc8nKBnp/JT6igTs82qPXz069H8I/A==

adm-zip@^0.5.5:
  version "0.5.9"
  resolved "https://registry.npmjs.org/adm-zip/-/adm-zip-0.5.9.tgz"
  integrity sha512-s+3fXLkeeLjZ2kLjCBwQufpI5fuN+kIGBxu6530nVQZGVol0d7Y/M88/xw9HGGUcJjKf8LutN3VPRUBq6N7Ajg==

after@0.8.2:
  version "0.8.2"
  resolved "https://registry.npmjs.org/after/-/after-0.8.2.tgz"
  integrity sha512-QbJ0NTQ/I9DI3uSJA4cbexiwQeRAfjPScqIbSjUDd9TOrcg6pTkdgziesOqxBMBzit8vFCTwrP27t13vFOORRA==

agent-base@6, agent-base@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

agentkeepalive@^4.2.1:
  version "4.2.1"
  resolved "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.2.1.tgz"
  integrity sha512-Zn4cw2NEqd+9fiSVWMscnjyQ1a8Yfoc5oBajLeo5w+YBHgDUcEBY2hS4YpTz6iN5f/2zQiktcuM6tS8x1p9dpA==
  dependencies:
    debug "^4.1.0"
    depd "^1.1.2"
    humanize-ms "^1.2.1"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.11.0:
  version "8.11.0"
  resolved "https://registry.npmjs.org/ajv/-/ajv-8.11.0.tgz"
  integrity sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-align@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/ansi-align/-/ansi-align-3.0.1.tgz"
  integrity sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==
  dependencies:
    string-width "^4.1.0"

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0, ansi-styles@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
  integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

apidoc@^0.51.1:
  version "0.51.1"
  resolved "https://registry.npmjs.org/apidoc/-/apidoc-0.51.1.tgz"
  integrity sha512-0dcL7NSUWDibgQ051ne/wXT0Dp+ArD66jwofuurxzRAPMuf2cRwa7GxRm9xlGXbOLaU7dVSZDr8LVeVBdI/oTQ==
  dependencies:
    bootstrap "3.4.1"
    commander "^8.3.0"
    diff-match-patch "^1.0.5"
    esbuild-loader "^2.16.0"
    expose-loader "^3.1.0"
    fs-extra "^10.0.0"
    glob "^7.2.0"
    handlebars "^4.7.7"
    iconv-lite "^0.6.3"
    jquery "^3.6.0"
    klaw-sync "^6.0.0"
    lodash "^4.17.21"
    markdown-it "^12.2.0"
    nodemon "^2.0.15"
    prismjs "^1.25.0"
    semver "^7.3.5"
    style-loader "^3.3.1"
    webpack "^5.64.2"
    webpack-cli "^4.9.1"
    winston "^3.3.3"

append-field@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz"
  integrity sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==

"aproba@^1.0.3 || ^2.0.0", aproba@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

arch@^2.1.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/arch/-/arch-2.2.0.tgz#1bc47818f305764f23ab3306b0bfc086c5a29d11"
  integrity sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==

archive-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/archive-type/-/archive-type-4.0.0.tgz"
  integrity sha512-zV4Ky0v1F8dBrdYElwTvQhweQ0P7Kwc1aluqJsYtOBP01jXcWCyW2IEfI1YiqsG+Iy7ZR+o5LF1N+PGECBxHWA==
  dependencies:
    file-type "^4.2.0"

archiver-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/archiver-utils/-/archiver-utils-2.1.0.tgz"
  integrity sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==
  dependencies:
    glob "^7.1.4"
    graceful-fs "^4.2.0"
    lazystream "^1.0.0"
    lodash.defaults "^4.2.0"
    lodash.difference "^4.5.0"
    lodash.flatten "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.union "^4.6.0"
    normalize-path "^3.0.0"
    readable-stream "^2.0.0"

archiver@^5.3.0, archiver@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/archiver/-/archiver-5.3.1.tgz"
  integrity sha512-8KyabkmbYrH+9ibcTScQ1xCJC/CGcugdVIwB+53f5sZziXgwUh3iXlAlANMxcZyDEfTHMe6+Z5FofV8nopXP7w==
  dependencies:
    archiver-utils "^2.1.0"
    async "^3.2.3"
    buffer-crc32 "^0.2.1"
    readable-stream "^3.6.0"
    readdir-glob "^1.0.0"
    tar-stream "^2.2.0"
    zip-stream "^4.1.0"

archy@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/archy/-/archy-1.0.0.tgz"
  integrity sha512-Xg+9RwCg/0p32teKdGMPTPnVXKD0w3DfHnFTficozsAgsvq2XenPJq/MYpzzQ/v8zrOyJn6Ds39VA4JIDwFfqw==

are-we-there-yet@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-3.0.0.tgz"
  integrity sha512-0GWpv50YSOcLXaN6/FAKY3vfRbllXWV2xvfA/oKJF8pzFhWXPV+yjhJXDBbjscDYowv7Yw1A3uigpzn5iEGTyw==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

arraybuffer.slice@~0.0.7:
  version "0.0.7"
  resolved "https://registry.npmjs.org/arraybuffer.slice/-/arraybuffer.slice-0.0.7.tgz"
  integrity sha512-wGUIVQXuehL5TCqQun8OW81jGzAWycqzFF8lFp+GOM5BXLYj3bKNsYC4daB7n6XjCqxQA/qgTJ+8ANR3acjrog==

asap@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

async@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmjs.org/async/-/async-1.5.2.tgz"
  integrity sha512-nSVgobk4rv61R9PUSDtYt7mPVB2olxNR5RWJcAsH676/ef11bUZwvu7+RGYrYauVdDPcO519v68wRhXQtxsV9w==

async@^3.2.3:
  version "3.2.4"
  resolved "https://registry.npmjs.org/async/-/async-3.2.4.tgz"
  integrity sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz"
  integrity sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==

await-to-js@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/await-to-js/-/await-to-js-3.0.0.tgz"
  integrity sha512-zJAaP9zxTcvTHRlejau3ZOY4V7SRpiByf3/dxx2uyKxxor19tpmpV2QRsTKikckwhaPmr2dVpxxMr7jOCYVp5g==

aws-sdk@^2.1147.0, aws-sdk@^2.1168.0:
  version "2.1168.0"
  resolved "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.1168.0.tgz"
  integrity sha512-9+WYoYTHHjLqeWdSSLbNpmc/NgnZpX4LGiyYjXenh4WNRBXshXI0XioTK8BQgDscVzB978EJV8kG1nZGE3USzw==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    uuid "8.0.0"
    xml2js "0.4.19"

axios@^0.21.1:
  version "0.21.4"
  resolved "https://registry.npmjs.org/axios/-/axios-0.21.4.tgz"
  integrity sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==
  dependencies:
    follow-redirects "^1.14.0"

axios@^0.27.2:
  version "0.27.2"
  resolved "https://registry.npmjs.org/axios/-/axios-0.27.2.tgz"
  integrity sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

backo2@1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/backo2/-/backo2-1.0.2.tgz"
  integrity sha512-zj6Z6M7Eq+PBZ7PQxl5NT665MvJdAkzp0f60nAJ+sLaSCBPMwVak5ZegFbgVCzFcCJTKFoMizvM5Ld7+JrRJHA==

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-arraybuffer@0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.4.tgz"
  integrity sha512-a1eIFi4R9ySrbiMuyTGx5e92uRH5tQY6kArNcFaKBUleIoLjdjBg7Zxm3Mqm3Kmkf27HLR/1fnxX9q8GQ7Iavg==

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

base64id@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz"
  integrity sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==

basic-auth@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/basic-auth/-/basic-auth-2.0.1.tgz#b998279bf47ce38344b4f3cf916d4679bbf51e3a"
  integrity sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==
  dependencies:
    safe-buffer "5.1.2"

bcryptjs@^2.4.3:
  version "2.4.3"
  resolved "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz"
  integrity sha1-mrVie5PmBiH/fNrF2pczAn3x0Ms=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

bin-check@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/bin-check/-/bin-check-4.1.0.tgz#fc495970bdc88bb1d5a35fc17e65c4a149fc4a49"
  integrity sha512-b6weQyEUKsDGFlACWSIOfveEnImkJyK/FGW6FAG42loyoquvjdtOIqO6yBFzHyqyVVhNgNkQxxx09SFLK28YnA==
  dependencies:
    execa "^0.7.0"
    executable "^4.1.0"

bin-links@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/bin-links/-/bin-links-3.0.1.tgz"
  integrity sha512-9vx+ypzVhASvHTS6K+YSGf7nwQdANoz7v6MTC0aCtYnOEZ87YvMf81aY737EZnGZdpbRM3sfWjO9oWkKmuIvyQ==
  dependencies:
    cmd-shim "^5.0.0"
    mkdirp-infer-owner "^2.0.0"
    npm-normalize-package-bin "^1.0.0"
    read-cmd-shim "^3.0.0"
    rimraf "^3.0.0"
    write-file-atomic "^4.0.0"

bin-version-check@^5.0.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/bin-version-check/-/bin-version-check-5.1.0.tgz#788e80e036a87313f8be7908bc20e5abe43f0837"
  integrity sha512-bYsvMqJ8yNGILLz1KP9zKLzQ6YpljV3ln1gqhuLkUtyfGi3qXKGuK2p+U4NAvjVFzDFiBBtOpCOSFNuYYEGZ5g==
  dependencies:
    bin-version "^6.0.0"
    semver "^7.5.3"
    semver-truncate "^3.0.0"

bin-version@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/bin-version/-/bin-version-6.0.0.tgz#08ecbe5fc87898b441425e145f9e105064d00315"
  integrity sha512-nk5wEsP4RiKjG+vF+uG8lFsEn4d7Y6FVDamzzftSunXOoOcOOkzcWdKVlGgFFwlUQCj63SgnUkLLGF8v7lufhw==
  dependencies:
    execa "^5.0.0"
    find-versions "^5.0.0"

binary-extensions@^2.0.0, binary-extensions@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

bl@^1.0.0:
  version "1.2.3"
  resolved "https://registry.npmjs.org/bl/-/bl-1.2.3.tgz"
  integrity sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww==
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^4.0.3, bl@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

blob@0.0.5:
  version "0.0.5"
  resolved "https://registry.npmjs.org/blob/-/blob-0.0.5.tgz"
  integrity sha512-gaqbzQPqOoamawKg0LGVd7SzLgXS+JH61oWprSLH+P+abTczqJbhTR8CmJ2u9/bUYNmHTGJx/UEmn6doAvvuig==

bluebird@^3.5.1, bluebird@^3.7.2:
  version "3.7.2"
  resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

body-parser@1.20.0:
  version "1.20.0"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz"
  integrity sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.10.3"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

bootstrap@3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/bootstrap/-/bootstrap-3.4.1.tgz"
  integrity sha512-yN5oZVmRCwe5aKwzRj6736nSmKDX7pLYwsXiCj/EYmo16hODaBiT4En5btW/jhBF/seV+XMx3aYwukYC3A49DA==

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

boxen@^5.0.0:
  version "5.1.2"
  resolved "https://registry.npmjs.org/boxen/-/boxen-5.1.2.tgz"
  integrity sha512-9gYgQKXx+1nP8mP7CzFyaUARhg7D3n1dF/FnErWmu9l6JvGpNUN278h0aSb+QjoiKSWG+iZ3uHrcqk0qrY9RQQ==
  dependencies:
    ansi-align "^3.0.0"
    camelcase "^6.2.0"
    chalk "^4.1.0"
    cli-boxes "^2.2.1"
    string-width "^4.2.2"
    type-fest "^0.20.2"
    widest-line "^3.1.0"
    wrap-ansi "^7.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.14.5:
  version "4.21.0"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.21.0.tgz"
  integrity sha512-UQxE0DIhRB5z/zDz9iA03BOfxaN2+GQdBYH/2WrSIWEUrnpzTPJbhqt+umq6r3acaPRTW1FNTkrcp0PXgtFkvA==
  dependencies:
    caniuse-lite "^1.0.30001358"
    electron-to-chromium "^1.4.164"
    node-releases "^2.0.5"
    update-browserslist-db "^1.0.0"

bson@^1.1.1:
  version "1.1.6"
  resolved "https://registry.npmjs.org/bson/-/bson-1.1.6.tgz"
  integrity sha512-EvVNVeGo4tHxwi8L6bPj3y3itEvStdwvvlojVxxbyYfoaxJ6keLgrTuKdyfEAszFK+H3olzBuafE0yoh0D1gdg==

bson@^4.6.2:
  version "4.6.3"
  resolved "https://registry.npmjs.org/bson/-/bson-4.6.3.tgz"
  integrity sha512-rAqP5hcUVJhXP2MCSNVsf0oM2OGU1So6A9pVRDYayvJ5+hygXHQApf87wd5NlhPM1J9RJnbqxIG/f8QTzRoQ4A==
  dependencies:
    buffer "^5.6.0"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz"
  integrity sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg==

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/buffer-alloc/-/buffer-alloc-1.2.0.tgz"
  integrity sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@^0.2.1, buffer-crc32@^0.2.13, buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  integrity sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/buffer-fill/-/buffer-fill-1.0.0.tgz"
  integrity sha512-T7zexNBwiiaCOGDg9xNX9PBmjrubblRkENuptryuI64URkXDFum9il/JGL8Lm8wYfAXpredVXXZz7eMHilimiQ==

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.2.1, buffer@^5.5.0, buffer@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builtin-modules@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/builtin-modules/-/builtin-modules-3.3.0.tgz"
  integrity sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==

builtins@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/builtins/-/builtins-1.0.3.tgz"
  integrity sha512-uYBjakWipfaO/bXI7E8rq6kpwHRZK5cNYrUv2OzZSI/FvmdMyXJ2tG9dKcjEC5YHmHpUAwsargWIZNWdxb/bnQ==

builtins@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/builtins/-/builtins-5.0.1.tgz"
  integrity sha512-qwVpFEHNfhYJIzNRBvd2C1kyo6jz3ZSMPyyuR47OPdiKWlbYnZNyDWuyR175qDnAJLiCo5fBBqPb3RiXgWlkOQ==
  dependencies:
    semver "^7.0.0"

busboy@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
  dependencies:
    streamsearch "^1.1.0"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  integrity sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cacache@^16.0.0, cacache@^16.0.6, cacache@^16.1.0, cacache@^16.1.1:
  version "16.1.1"
  resolved "https://registry.npmjs.org/cacache/-/cacache-16.1.1.tgz"
  integrity sha512-VDKN+LHyCQXaaYZ7rA/qtkURU+/yYhviUdvqEv2LT6QPZU8jpyzEkEVAcKlKLt5dJ5BRp11ym8lo3NKLluEPLg==
  dependencies:
    "@npmcli/fs" "^2.1.0"
    "@npmcli/move-file" "^2.0.0"
    chownr "^2.0.0"
    fs-minipass "^2.1.0"
    glob "^8.0.1"
    infer-owner "^1.0.4"
    lru-cache "^7.7.1"
    minipass "^3.1.6"
    minipass-collect "^1.0.2"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    mkdirp "^1.0.4"
    p-map "^4.0.0"
    promise-inflight "^1.0.1"
    rimraf "^3.0.2"
    ssri "^9.0.0"
    tar "^6.1.11"
    unique-filename "^1.1.1"

cacheable-lookup@^5.0.3:
  version "5.0.4"
  resolved "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.4.tgz"
  integrity sha512-2/kNscPhpcxrOigMZzbiWF7dz8ilhb/nIHU3EyZiXWXpeq/au8qJ8VhdftMkty3n7Gj6HIGalQG8oiBNB3AJgA==

cacheable-request@^6.0.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/cacheable-request/-/cacheable-request-6.1.0.tgz"
  integrity sha512-Oj3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==
  dependencies:
    clone-response "^1.0.2"
    get-stream "^5.1.0"
    http-cache-semantics "^4.0.0"
    keyv "^3.0.0"
    lowercase-keys "^2.0.0"
    normalize-url "^4.1.0"
    responselike "^1.0.2"

cacheable-request@^7.0.2:
  version "7.0.2"
  resolved "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.2.tgz"
  integrity sha512-pouW8/FmiPQbuGpkXQ9BAPv/Mo5xDGANgSNXzTzJ8DrKGuXOssM4wIQRjfanNRh3Yu5cfYPvcorqbhg2KIJtew==
  dependencies:
    clone-response "^1.0.2"
    get-stream "^5.1.0"
    http-cache-semantics "^4.0.0"
    keyv "^4.0.0"
    lowercase-keys "^2.0.0"
    normalize-url "^6.0.1"
    responselike "^2.0.0"

cachedir@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/cachedir/-/cachedir-2.3.0.tgz"
  integrity sha512-A+Fezp4zxnit6FanDmv9EqXNAi3vt9DWp51/71UEhXukb7QUuvtv9344h91dyAxuTLoSYJFU299qzR3tzwPAhw==

call-bind@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
  integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001358:
  version "1.0.30001359"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001359.tgz"
  integrity sha512-Xln/BAsPzEuiVLgJ2/45IaqD9jShtk3Y33anKb4+yLwQzws3+v6odKfpgES/cDEaZMLzSChpIGdbOYtH9MyuHw==

chalk@^2.3.1:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==

child-process-ext@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/child-process-ext/-/child-process-ext-2.1.1.tgz"
  integrity sha512-0UQ55f51JBkOFa+fvR76ywRzxiPwQS3Xe8oe5bZRphpv+dIMeerW5Zn5e4cUy4COJwVtJyU0R79RMnw+aCqmGA==
  dependencies:
    cross-spawn "^6.0.5"
    es5-ext "^0.10.53"
    log "^6.0.0"
    split2 "^3.1.1"
    stream-promise "^3.2.0"

chokidar@^3.5.1, chokidar@^3.5.2, chokidar@^3.5.3:
  version "3.5.3"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  integrity sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz"
  integrity sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==

ci-info@^3.3.1:
  version "3.3.2"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-3.3.2.tgz"
  integrity sha512-xmDt/QIAdeZ9+nfdPsaBCpMvHNLFiLdjj59qjqn+6iPe6YmHGQ35sBnQ8uslRBXFmXkiZQOJRjvQeoGppoTjjg==

cidr-regex@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/cidr-regex/-/cidr-regex-3.1.1.tgz"
  integrity sha512-RBqYd32aDwbCMFJRL6wHOlDNYJsPNTt8vC82ErHF5vKt8QQzxm1FrkW8s/R5pVrXMf17sba09Uoy91PKiddAsw==
  dependencies:
    ip-regex "^4.1.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cli-boxes@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/cli-boxes/-/cli-boxes-2.2.1.tgz"
  integrity sha512-y4coMcylgSCdVinjiDBuR8PCC2bLjyGTwEmPb9NHR/QaNU6EUOXcTY/s6VjGMD6ENSEaeQYHCY0GNGS5jfMwPw==

cli-color@^2.0.1, cli-color@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/cli-color/-/cli-color-2.0.2.tgz"
  integrity sha512-g4JYjrTW9MGtCziFNjkqp3IMpGhnJyeB0lOtRPjQkYhXzKYr6tYnXKyEVnMzITxhpbahsEW9KsxOYIDKwcsIBw==
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.59"
    es6-iterator "^2.0.3"
    memoizee "^0.4.15"
    timers-ext "^0.1.7"

cli-columns@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cli-columns/-/cli-columns-4.0.0.tgz"
  integrity sha512-XW2Vg+w+L9on9wtwKpyzluIPCWXjaBahI7mTcYjx+BVIYD9c3yqcv/yKC7CmdCZat4rq2yiE1UMSJC5ivKfMtQ==
  dependencies:
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
  dependencies:
    restore-cursor "^3.1.0"

cli-progress-footer@^2.3.1, cli-progress-footer@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/cli-progress-footer/-/cli-progress-footer-2.3.2.tgz"
  integrity sha512-uzHGgkKdeA9Kr57eyH1W5HGiNShP8fV1ETq04HDNM1Un6ShXbHhwi/H8LNV9L1fQXKjEw0q5FUkEVNuZ+yZdSw==
  dependencies:
    cli-color "^2.0.2"
    d "^1.0.1"
    es5-ext "^0.10.61"
    mute-stream "0.0.8"
    process-utils "^4.0.0"
    timers-ext "^0.1.7"
    type "^2.6.0"

cli-spinners@^2.5.0:
  version "2.6.1"
  resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.6.1.tgz"
  integrity sha512-x/5fWmGMnbKQAaNwN+UZlV79qBLM9JFnJuJ03gIi5whrob0xV0ofNVHy9DhwGdsMJQc2OKv0oGmLzvaqvAVv+g==

cli-sprintf-format@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/cli-sprintf-format/-/cli-sprintf-format-1.1.1.tgz"
  integrity sha512-BbEjY9BEdA6wagVwTqPvmAwGB24U93rQPBFZUT8lNCDxXzre5LFHQUTJc70czjgUomVg8u8R5kW8oY9DYRFNeg==
  dependencies:
    cli-color "^2.0.1"
    es5-ext "^0.10.53"
    sprintf-kit "^2.0.1"
    supports-color "^6.1.0"

cli-table3@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.2.tgz"
  integrity sha512-QyavHCaIC80cMivimWu4aWHilIpiDpfm3hGmqAmXVL1UsnbLuBSMd21hTX6VY4ZSDSM73ESLeF8TOYId3rBTbw==
  dependencies:
    string-width "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz"
  integrity sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz"
  integrity sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone-response@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/clone-response/-/clone-response-1.0.2.tgz"
  integrity sha512-yjLXh88P599UOyPTFX0POsd7WxnbsVsGohcwzHOLspIhhpalPw1BcqED8NblyZLKcGrL8dTgMlcaZxV2jAD41Q==
  dependencies:
    mimic-response "^1.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

cluster-key-slot@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/cluster-key-slot/-/cluster-key-slot-1.1.0.tgz"
  integrity sha512-2Nii8p3RwAPiFwsnZvukotvow2rIHM+yQ6ZcBXGHdniadkYGZYiGmkHJIbZPIV9nfv7m/U1IPMVVcAhoWFeklw==

cmd-shim@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/cmd-shim/-/cmd-shim-5.0.0.tgz"
  integrity sha512-qkCtZ59BidfEwHltnJwkyVZn+XQojdAySM1D1gSeh11Z4pW1Kpolkyo53L5noc0nrxmIvyFwTmJRo4xs7FFLPw==
  dependencies:
    mkdirp-infer-owner "^2.0.0"

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.6.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color-support@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

color@^3.1.3:
  version "3.2.1"
  resolved "https://registry.npmjs.org/color/-/color-3.2.1.tgz"
  integrity sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colorette@^2.0.14:
  version "2.0.19"
  resolved "https://registry.npmjs.org/colorette/-/colorette-2.0.19.tgz"
  integrity sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==

colorspace@1.1.x:
  version "1.1.4"
  resolved "https://registry.npmjs.org/colorspace/-/colorspace-1.1.4.tgz"
  integrity sha512-BgvKJiuVu1igBUF2kEjRCZXol6wiiGbY5ipL/oVPwm0BL9sIpMIzM8IK7vwuxIIzOXMV3Ey5w+vxhm0rR/TN8w==
  dependencies:
    color "^3.1.3"
    text-hex "1.0.x"

columnify@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/columnify/-/columnify-1.6.0.tgz"
  integrity sha512-lomjuFZKfM6MSAnV9aCZC9sc0qGbmZdfygNv+nCpqVkSKdCxCklLtd16O0EILGkImHw9ZpHkAnHaB+8Zxq5W6Q==
  dependencies:
    strip-ansi "^6.0.1"
    wcwidth "^1.0.0"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.0, commander@^2.8.1:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@^7.0.0, commander@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz"
  integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==

commander@^8.3.0:
  version "8.3.0"
  resolved "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz"
  integrity sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==

commander@~4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

common-ancestor-path@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/common-ancestor-path/-/common-ancestor-path-1.0.1.tgz"
  integrity sha512-L3sHRo1pXXEqX8VU28kfgUY+YGsk09hPqZiZmLacNib6XNTCM8ubYeT7ryXQw8asB1sKgcU5lkB7ONug08aB8w==

component-bind@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/component-bind/-/component-bind-1.0.0.tgz"
  integrity sha512-WZveuKPeKAG9qY+FkYDeADzdHyTYdIboXS59ixDeRJL5ZhxpqUnxSOwop4FQjMsiYm3/Or8cegVbpAHNA7pHxw==

component-emitter@1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz"
  integrity sha512-jPatnhd33viNplKjqXKRkGU345p263OIWzDL2wH3LGIGp5Kojo+uXizHmOADRvhGFFTnJqX3jBAKP6vvmSDKcA==

component-emitter@^1.3.0, component-emitter@~1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz"
  integrity sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==

component-inherit@0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/component-inherit/-/component-inherit-0.0.3.tgz"
  integrity sha512-w+LhYREhatpVqTESyGFg3NlP6Iu0kEKUHETY9GoZP/pQyW4mHFZuFWRUCIqVPZ36ueVLtoOEZaAqbCF2RDndaA==

compress-brotli@^1.3.8:
  version "1.3.8"
  resolved "https://registry.npmjs.org/compress-brotli/-/compress-brotli-1.3.8.tgz"
  integrity sha512-lVcQsjhxhIXsuupfy9fmZUFtAIdBmXA7EGY6GBdgZ++qkM9zG4YFT8iU7FoBxzryNDMOpD1HIFHUSX4D87oqhQ==
  dependencies:
    "@types/json-buffer" "~3.0.0"
    json-buffer "~3.0.1"

compress-commons@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/compress-commons/-/compress-commons-4.1.1.tgz"
  integrity sha512-QLdDLCKNV2dtoTorqgxngQCMA+gWXkM/Nwu7FpeBhk/RdkzimqC3jueb/FDmaZeXh+uby1jkBqE3xArsLBE5wQ==
  dependencies:
    buffer-crc32 "^0.2.13"
    crc32-stream "^4.0.2"
    normalize-path "^3.0.0"
    readable-stream "^3.6.0"

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

concat-stream@^1.5.2:
  version "1.6.2"
  resolved "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz"
  integrity sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

config@^3.3.6:
  version "3.3.7"
  resolved "https://registry.npmjs.org/config/-/config-3.3.7.tgz"
  integrity sha512-mX/n7GKDYZMqvvkY6e6oBY49W8wxdmQt+ho/5lhwFDXqQW9gI+Ahp8EKp8VAbISPnmf2+Bv5uZK7lKXZ6pf1aA==
  dependencies:
    json5 "^2.1.1"

configstore@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/configstore/-/configstore-5.0.1.tgz"
  integrity sha512-aMKprgk5YhBNyH25hj8wGt2+D52Sw1DRRIzqBwLp2Ya9mFmY8KPvvtvmna8SxVR9JMZ4kzMD68N22vlaRpkeFA==
  dependencies:
    dot-prop "^5.2.0"
    graceful-fs "^4.1.2"
    make-dir "^3.0.0"
    unique-string "^2.0.0"
    write-file-atomic "^3.0.0"
    xdg-basedir "^4.0.0"

console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

content-disposition@0.5.4, content-disposition@^0.5.4:
  version "0.5.4"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz"
  integrity sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA==

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

cookie@~0.4.1:
  version "0.4.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz"
  integrity sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==

cookiejar@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.3.tgz"
  integrity sha512-JxbCBUdrfr6AQjOXrxoTvAMJO4HBTUIlBzslcJPAz+/KT8yk53fXun51u+RenNYvad/+Vc2DIz5o9UxlCDymFQ==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cors@^2.8.5:
  version "2.8.5"
  resolved "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
  dependencies:
    object-assign "^4"
    vary "^1"

crc-32@^1.2.0:
  version "1.2.2"
  resolved "https://registry.npmjs.org/crc-32/-/crc-32-1.2.2.tgz"
  integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==

crc32-stream@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/crc32-stream/-/crc32-stream-4.0.2.tgz"
  integrity sha512-DxFZ/Hk473b/muq1VJ///PMNLj0ZMnzye9thBpmjpJKCc5eMgB95aK8zCGrGfQ90cWo561Te6HK9D+j4KPdM6w==
  dependencies:
    crc-32 "^1.2.0"
    readable-stream "^3.4.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

cron@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/cron/-/cron-2.0.0.tgz"
  integrity sha512-RPeRunBCFr/WEo7WLp8Jnm45F/ziGJiHVvVQEBSDTSGu6uHW49b2FOP2O14DcXlGJRLhwE7TIoDzHHK4KmlL6g==
  dependencies:
    luxon "^1.23.x"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
  integrity sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==

csvtojson@^2.0.10:
  version "2.0.10"
  resolved "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.10.tgz"
  integrity sha512-lUWFxGKyhraKCW8Qghz6Z0f2l/PqB1W3AO0HKJzGIQ5JRSlR651ekJDiGJbBT4sRNNv5ddnSGVEnsxP9XRCVpQ==
  dependencies:
    bluebird "^3.5.1"
    lodash "^4.17.3"
    strip-bom "^2.0.0"

d@1, d@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/d/-/d-1.0.1.tgz"
  integrity sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

dayjs@^1.11.2:
  version "1.11.3"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.3.tgz"
  integrity sha512-xxwlswWOlGhzgQ4TKzASQkUhqERI3egRNqgV4ScR8wlANA/A9tZ7miXa44vTTKEq5l7vWoL5G57bG3zA+Kow0A==

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@4, debug@4.x, debug@^4, debug@^4.1.0, debug@^4.1.1, debug@^4.3.3, debug@^4.3.4, debug@~4.3.1:
  version "4.3.4"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@~3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
  integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
  dependencies:
    ms "2.0.0"

debug@~4.1.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

debuglog@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/debuglog/-/debuglog-1.0.1.tgz"
  integrity sha512-syBZ+rnAK3EgMsH2aYEOLUW7mZSY9Gb+0wUMCFsZvcmiz+HigA0LOcq/HoQqVuGG+EKykunc7QG2bzrponfaSw==

decompress-response@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-3.3.0.tgz"
  integrity sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==
  dependencies:
    mimic-response "^1.0.0"

decompress-response@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz"
  integrity sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==
  dependencies:
    mimic-response "^3.1.0"

decompress-tar@^4.0.0, decompress-tar@^4.1.0, decompress-tar@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/decompress-tar/-/decompress-tar-4.1.1.tgz"
  integrity sha512-JdJMaCrGpB5fESVyxwpCx4Jdj2AagLmv3y58Qy4GE6HMVjWz1FeVQk1Ct4Kye7PftcdOo/7U7UKzYBJgqnGeUQ==
  dependencies:
    file-type "^5.2.0"
    is-stream "^1.1.0"
    tar-stream "^1.5.2"

decompress-tarbz2@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/decompress-tarbz2/-/decompress-tarbz2-4.1.1.tgz"
  integrity sha512-s88xLzf1r81ICXLAVQVzaN6ZmX4A6U4z2nMbOwobxkLoIIfjVMBg7TeguTUXkKeXni795B6y5rnvDw7rxhAq9A==
  dependencies:
    decompress-tar "^4.1.0"
    file-type "^6.1.0"
    is-stream "^1.1.0"
    seek-bzip "^1.0.5"
    unbzip2-stream "^1.0.9"

decompress-targz@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/decompress-targz/-/decompress-targz-4.1.1.tgz"
  integrity sha512-4z81Znfr6chWnRDNfFNqLwPvm4db3WuZkqV+UgXQzSngG3CEKdBkw5jrv3axjjL96glyiiKjsxJG3X6WBZwX3w==
  dependencies:
    decompress-tar "^4.1.1"
    file-type "^5.2.0"
    is-stream "^1.1.0"

decompress-unzip@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/decompress-unzip/-/decompress-unzip-4.0.1.tgz"
  integrity sha512-1fqeluvxgnn86MOh66u8FjbtJpAFv5wgCT9Iw8rcBqQcCo5tO8eiJw7NNTrvt9n4CRBVq7CstiS922oPgyGLrw==
  dependencies:
    file-type "^3.8.0"
    get-stream "^2.2.0"
    pify "^2.3.0"
    yauzl "^2.4.2"

decompress@^4.2.1:
  version "4.2.1"
  resolved "https://registry.npmjs.org/decompress/-/decompress-4.2.1.tgz"
  integrity sha512-e48kc2IjU+2Zw8cTb6VZcJQ3lgVbS4uuB1TfCHbiZIP/haNXm+SVyhu+87jts5/3ROpd82GSVCoNs/z8l4ZOaQ==
  dependencies:
    decompress-tar "^4.0.0"
    decompress-tarbz2 "^4.0.0"
    decompress-targz "^4.0.0"
    decompress-unzip "^4.0.1"
    graceful-fs "^4.1.10"
    make-dir "^1.0.0"
    pify "^2.3.0"
    strip-dirs "^2.0.0"

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz"
  integrity sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.3.tgz"
  integrity sha512-s82itHOnYrN0Ib8r+z7laQz3sdE+4FP3d9Q7VLO7U+KRT+CR0GsWuyHxzdAY82I7cXv0G/twrqomTJLOssO5HA==
  dependencies:
    clone "^1.0.2"

defer-to-connect@^1.0.1:
  version "1.1.3"
  resolved "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.3.tgz"
  integrity sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==

defer-to-connect@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-2.0.1.tgz"
  integrity sha512-4tvttepXG1VaYGrRibk5EwJd1t4udunSOVMdLSAL6mId1ix438oPwPZMALY41FCijukO1L0twNcGsdzS7dHgDg==

deferred@^0.7.11:
  version "0.7.11"
  resolved "https://registry.npmjs.org/deferred/-/deferred-0.7.11.tgz"
  integrity sha512-8eluCl/Blx4YOGwMapBvXRKxHXhA8ejDXYzEaK8+/gtcm8hRMhSLmXSqDmNUKNc/C8HNSmuyyp/hflhqDAvK2A==
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.50"
    event-emitter "^0.3.5"
    next-tick "^1.0.0"
    timers-ext "^0.1.7"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

denque@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/denque/-/denque-2.0.1.tgz"
  integrity sha512-tfiWc6BQLXNLpNiR5iGd0Ocu3P3VpxfzFiqubLgMfhfOw9WyvgJBd46CClNn9k3qfbjvT//0cf7AlYRX/OslMQ==

depd@2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

dezalgo@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.3.tgz"
  integrity sha512-K7i4zNfT2kgQz3GylDw40ot9GAE47sFZ9EXHFSPP6zONLgH6kWXE0KWJchkbQJLBkRazq4APwZ4OwiFFlT95OQ==
  dependencies:
    asap "^2.0.0"
    wrappy "1"

dezalgo@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.4.tgz"
  integrity sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff-match-patch@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/diff-match-patch/-/diff-match-patch-1.0.5.tgz"
  integrity sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==

diff-sequences@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.5.1.tgz"
  integrity sha512-k1gCAXAsNgLwEL+Y8Wvl+M6oEFj5bgazfZULpS5CneoPPXRaCCW7dm+q21Ky2VEE5X+VeRDBVg1Pcvvsr4TtNQ==

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

diff@^5.0.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/diff/-/diff-5.1.0.tgz#bc52d298c5ea8df9194800224445ed43ffc87e40"
  integrity sha512-D+mk+qE8VC/PAUrlAU34N+VfXev0ghe5ywmpqrawphmVZc1bEfn56uo9qpyGp1p4xpzOHkSW4ztBd6L7Xx4ACw==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/dot-prop/-/dot-prop-5.3.0.tgz"
  integrity sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  integrity sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==

dotenv@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz"
  integrity sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==

double-ended-queue@^2.1.0-0:
  version "2.1.0-0"
  resolved "https://registry.npmjs.org/double-ended-queue/-/double-ended-queue-2.1.0-0.tgz"
  integrity sha512-+BNfZ+deCo8hMNpDqDnvT+c0XpJ5cUa6mqYq89bho2Ifze4URTqRkcwR399hWoTrTkbZ/XJYDgP6rc7pRgffEQ==

duplexer3@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/duplexer3/-/duplexer3-0.1.4.tgz"
  integrity sha512-CEj8FwwNA4cVH2uFCoHUrmojhYh1vmCdOaneKJXwkeY1i9jnlslVo9dx+hQ5Hl9GnH/Bwy/IjxAyOePyPKYnzA==

duration@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/duration/-/duration-0.2.2.tgz"
  integrity sha512-06kgtea+bGreF5eKYgI/36A6pLXggY7oR4p1pq4SmdFBn1ReOL5D8RhG64VrqfTTKNucqqtBAwEj8aB88mcqrg==
  dependencies:
    d "1"
    es5-ext "~0.10.46"

dynamic-dedupe@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/dynamic-dedupe/-/dynamic-dedupe-0.3.0.tgz"
  integrity sha512-ssuANeD+z97meYOqd50e04Ze5qp4bPqo8cCkI4TRjZkzAUgIDTrXV1R8QCdINpiI+hw14+rYazvTRdQrz0/rFQ==
  dependencies:
    xtend "^4.0.0"

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.4.164:
  version "1.4.170"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.170.tgz"
  integrity sha512-rZ8PZLhK4ORPjFqLp9aqC4/S1j4qWFsPPz13xmWdrbBkU/LlxMcok+f+6f8YnQ57MiZwKtOaW15biZZsY5Igvw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

enabled@2.0.x:
  version "2.0.0"
  resolved "https://registry.npmjs.org/enabled/-/enabled-2.0.0.tgz"
  integrity sha512-AKrN98kuwOzMIdAizXGI86UFBoo26CL21UM763y1h/GMSJ4/OHU9k2YlsmBpyScFo/wbLzWQJBMCW4+IO3/+OQ==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.13:
  version "0.1.13"
  resolved "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz"
  integrity sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.0.0, end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

engine.io-client@~3.5.0:
  version "3.5.2"
  resolved "https://registry.npmjs.org/engine.io-client/-/engine.io-client-3.5.2.tgz"
  integrity sha512-QEqIp+gJ/kMHeUun7f5Vv3bteRHppHH/FMBQX/esFj/fuYfjyUKWGMo3VCvIP/V8bE9KcjHmRZrhIz2Z9oNsDA==
  dependencies:
    component-emitter "~1.3.0"
    component-inherit "0.0.3"
    debug "~3.1.0"
    engine.io-parser "~2.2.0"
    has-cors "1.1.0"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    ws "~7.4.2"
    xmlhttprequest-ssl "~1.6.2"
    yeast "0.1.2"

engine.io-parser@~2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-2.2.1.tgz"
  integrity sha512-x+dN/fBH8Ro8TFwJ+rkB2AmuVw9Yu2mockR/p3W8f8YtExwFgDvBDi0GWyb4ZLkpahtDGZgtr3zLovanJghPqg==
  dependencies:
    after "0.8.2"
    arraybuffer.slice "~0.0.7"
    base64-arraybuffer "0.1.4"
    blob "0.0.5"
    has-binary2 "~1.0.2"

engine.io@~3.5.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/engine.io/-/engine.io-3.5.0.tgz"
  integrity sha512-21HlvPUKaitDGE4GXNtQ7PLP0Sz4aWLddMPw2VTyFz1FVZqu/kZsJUO8WNpKuE/OCL7nkfRaOui2ZCJloGznGA==
  dependencies:
    accepts "~1.3.4"
    base64id "2.0.0"
    cookie "~0.4.1"
    debug "~4.1.0"
    engine.io-parser "~2.2.0"
    ws "~7.4.2"

enhanced-resolve@^5.9.3:
  version "5.9.3"
  resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.9.3.tgz"
  integrity sha512-Bq9VSor+kjvW3f9/MiiR4eE3XYgOl7/rS8lnSxbRbF3kS0B2r+Y9w5krBWxZgDxASVZbdYrn5wT4j/Wb0J9qow==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz"
  integrity sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==

entities@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/entities/-/entities-2.1.0.tgz"
  integrity sha512-hCx1oky9PFrJ611mf0ifBLBRW8lUUVRlFolb5gWRfIELabBlbp9xZvrqZLZAs+NxFnbfQoeGd8wDkygjg7U85w==

env-paths@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz"
  integrity sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==

envinfo@^7.7.3:
  version "7.8.1"
  resolved "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz"
  integrity sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==

err-code@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz"
  integrity sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==

es-module-lexer@^0.9.0:
  version "0.9.3"
  resolved "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.3.tgz"
  integrity sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==

es5-ext@^0.10.12, es5-ext@^0.10.35, es5-ext@^0.10.46, es5-ext@^0.10.47, es5-ext@^0.10.49, es5-ext@^0.10.50, es5-ext@^0.10.53, es5-ext@^0.10.59, es5-ext@^0.10.61, es5-ext@~0.10.14, es5-ext@~0.10.2, es5-ext@~0.10.46:
  version "0.10.61"
  resolved "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.61.tgz"
  integrity sha512-yFhIqQAzu2Ca2I4SE2Au3rxVfmohU9Y7wqGR+s7+H7krk26NXhIRAZDgqd6xqjCEFUomDEA3/Bo/7fKmIkW1kA==
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    next-tick "^1.1.0"

es6-iterator@^2.0.3, es6-iterator@~2.0.1:
  version "2.0.3"
  resolved "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz"
  integrity sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-set@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npmjs.org/es6-set/-/es6-set-0.1.5.tgz"
  integrity sha512-7S8YXIcUfPMOr3rqJBVMePAbRsD1nWeSMQ86K/lDI76S3WKXz+KWILvTIPbTroubOkZTGh+b+7/xIIphZXNYbA==
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.1.tgz"
  integrity sha512-exfuQY8UGtn/N+gL1iKkH8fpNd5sJ760nJq6mmZAHldfxMD5kX07lbQuYlspoXsuknXNv9Fb7y2GsPOnQIbxHg==
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.3.tgz"
  integrity sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

es6-weak-map@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-2.0.3.tgz"
  integrity sha512-p5um32HOTO1kP+w7PRnB+5lQ43Z6muuMuIMffvDN8ZB4GcnjLBV6zGStpbASIMk4DCAvEaamhe2zhyCb/QXXsA==
  dependencies:
    d "1"
    es5-ext "^0.10.46"
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.1"

esbuild-android-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-android-64/-/esbuild-android-64-0.14.47.tgz"
  integrity sha512-R13Bd9+tqLVFndncMHssZrPWe6/0Kpv2/dt4aA69soX4PRxlzsVpCvoJeFE8sOEoeVEiBkI0myjlkDodXlHa0g==

esbuild-android-arm64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-android-arm64/-/esbuild-android-arm64-0.14.47.tgz"
  integrity sha512-OkwOjj7ts4lBp/TL6hdd8HftIzOy/pdtbrNA4+0oVWgGG64HrdVzAF5gxtJufAPOsEjkyh1oIYvKAUinKKQRSQ==

esbuild-darwin-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-darwin-64/-/esbuild-darwin-64-0.14.47.tgz"
  integrity sha512-R6oaW0y5/u6Eccti/TS6c/2c1xYTb1izwK3gajJwi4vIfNs1s8B1dQzI1UiC9T61YovOQVuePDcfqHLT3mUZJA==

esbuild-darwin-arm64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.14.47.tgz"
  integrity sha512-seCmearlQyvdvM/noz1L9+qblC5vcBrhUaOoLEDDoLInF/VQ9IkobGiLlyTPYP5dW1YD4LXhtBgOyevoIHGGnw==

esbuild-freebsd-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-freebsd-64/-/esbuild-freebsd-64-0.14.47.tgz"
  integrity sha512-ZH8K2Q8/Ux5kXXvQMDsJcxvkIwut69KVrYQhza/ptkW50DC089bCVrJZZ3sKzIoOx+YPTrmsZvqeZERjyYrlvQ==

esbuild-freebsd-arm64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.14.47.tgz"
  integrity sha512-ZJMQAJQsIOhn3XTm7MPQfCzEu5b9STNC+s90zMWe2afy9EwnHV7Ov7ohEMv2lyWlc2pjqLW8QJnz2r0KZmeAEQ==

esbuild-linux-32@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-32/-/esbuild-linux-32-0.14.47.tgz"
  integrity sha512-FxZOCKoEDPRYvq300lsWCTv1kcHgiiZfNrPtEhFAiqD7QZaXrad8LxyJ8fXGcWzIFzRiYZVtB3ttvITBvAFhKw==

esbuild-linux-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-64/-/esbuild-linux-64-0.14.47.tgz"
  integrity sha512-nFNOk9vWVfvWYF9YNYksZptgQAdstnDCMtR6m42l5Wfugbzu11VpMCY9XrD4yFxvPo9zmzcoUL/88y0lfJZJJw==

esbuild-linux-arm64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-arm64/-/esbuild-linux-arm64-0.14.47.tgz"
  integrity sha512-ywfme6HVrhWcevzmsufjd4iT3PxTfCX9HOdxA7Hd+/ZM23Y9nXeb+vG6AyA6jgq/JovkcqRHcL9XwRNpWG6XRw==

esbuild-linux-arm@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-arm/-/esbuild-linux-arm-0.14.47.tgz"
  integrity sha512-ZGE1Bqg/gPRXrBpgpvH81tQHpiaGxa8c9Rx/XOylkIl2ypLuOcawXEAo8ls+5DFCcRGt/o3sV+PzpAFZobOsmA==

esbuild-linux-mips64le@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.14.47.tgz"
  integrity sha512-mg3D8YndZ1LvUiEdDYR3OsmeyAew4MA/dvaEJxvyygahWmpv1SlEEnhEZlhPokjsUMfRagzsEF/d/2XF+kTQGg==

esbuild-linux-ppc64le@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.14.47.tgz"
  integrity sha512-WER+f3+szmnZiWoK6AsrTKGoJoErG2LlauSmk73LEZFQ/iWC+KhhDsOkn1xBUpzXWsxN9THmQFltLoaFEH8F8w==

esbuild-linux-riscv64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.14.47.tgz"
  integrity sha512-1fI6bP3A3rvI9BsaaXbMoaOjLE3lVkJtLxsgLHqlBhLlBVY7UqffWBvkrX/9zfPhhVMd9ZRFiaqXnB1T7BsL2g==

esbuild-linux-s390x@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-linux-s390x/-/esbuild-linux-s390x-0.14.47.tgz"
  integrity sha512-eZrWzy0xFAhki1CWRGnhsHVz7IlSKX6yT2tj2Eg8lhAwlRE5E96Hsb0M1mPSE1dHGpt1QVwwVivXIAacF/G6mw==

esbuild-loader@^2.16.0:
  version "2.19.0"
  resolved "https://registry.npmjs.org/esbuild-loader/-/esbuild-loader-2.19.0.tgz"
  integrity sha512-urGNVE6Tl2rqx92ElKi/LiExXjGvcH6HfDBFzJ9Ppwqh4n6Jmx8x7RKAyMzSM78b6CAaJLhDncG5sPrL0ROh5Q==
  dependencies:
    esbuild "^0.14.39"
    joycon "^3.0.1"
    json5 "^2.2.0"
    loader-utils "^2.0.0"
    tapable "^2.2.0"
    webpack-sources "^2.2.0"

esbuild-netbsd-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-netbsd-64/-/esbuild-netbsd-64-0.14.47.tgz"
  integrity sha512-Qjdjr+KQQVH5Q2Q1r6HBYswFTToPpss3gqCiSw2Fpq/ua8+eXSQyAMG+UvULPqXceOwpnPo4smyZyHdlkcPppQ==

esbuild-openbsd-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-openbsd-64/-/esbuild-openbsd-64-0.14.47.tgz"
  integrity sha512-QpgN8ofL7B9z8g5zZqJE+eFvD1LehRlxr25PBkjyyasakm4599iroUpaj96rdqRlO2ShuyqwJdr+oNqWwTUmQw==

esbuild-sunos-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-sunos-64/-/esbuild-sunos-64-0.14.47.tgz"
  integrity sha512-uOeSgLUwukLioAJOiGYm3kNl+1wJjgJA8R671GYgcPgCx7QR73zfvYqXFFcIO93/nBdIbt5hd8RItqbbf3HtAQ==

esbuild-windows-32@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-windows-32/-/esbuild-windows-32-0.14.47.tgz"
  integrity sha512-H0fWsLTp2WBfKLBgwYT4OTfFly4Im/8B5f3ojDv1Kx//kiubVY0IQunP2Koc/fr/0wI7hj3IiBDbSrmKlrNgLQ==

esbuild-windows-64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-windows-64/-/esbuild-windows-64-0.14.47.tgz"
  integrity sha512-/Pk5jIEH34T68r8PweKRi77W49KwanZ8X6lr3vDAtOlH5EumPE4pBHqkCUdELanvsT14yMXLQ/C/8XPi1pAtkQ==

esbuild-windows-arm64@0.14.47:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild-windows-arm64/-/esbuild-windows-arm64-0.14.47.tgz"
  integrity sha512-HFSW2lnp62fl86/qPQlqw6asIwCnEsEoNIL1h2uVMgakddf+vUuMcCbtUY1i8sst7KkgHrVKCJQB33YhhOweCQ==

esbuild@^0.14.39:
  version "0.14.47"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.14.47.tgz"
  integrity sha512-wI4ZiIfFxpkuxB8ju4MHrGwGLyp1+awEHAHVpx6w7a+1pmYIq8T9FGEVVwFo0iFierDoMj++Xq69GXWYn2EiwA==
  optionalDependencies:
    esbuild-android-64 "0.14.47"
    esbuild-android-arm64 "0.14.47"
    esbuild-darwin-64 "0.14.47"
    esbuild-darwin-arm64 "0.14.47"
    esbuild-freebsd-64 "0.14.47"
    esbuild-freebsd-arm64 "0.14.47"
    esbuild-linux-32 "0.14.47"
    esbuild-linux-64 "0.14.47"
    esbuild-linux-arm "0.14.47"
    esbuild-linux-arm64 "0.14.47"
    esbuild-linux-mips64le "0.14.47"
    esbuild-linux-ppc64le "0.14.47"
    esbuild-linux-riscv64 "0.14.47"
    esbuild-linux-s390x "0.14.47"
    esbuild-netbsd-64 "0.14.47"
    esbuild-openbsd-64 "0.14.47"
    esbuild-sunos-64 "0.14.47"
    esbuild-windows-32 "0.14.47"
    esbuild-windows-64 "0.14.47"
    esbuild-windows-arm64 "0.14.47"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==

escape-goat@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/escape-goat/-/escape-goat-2.1.1.tgz"
  integrity sha512-8/uIhbG12Csjy2JEW7D9pHbreaVaS/OpN3ycnyvElTdwM5n6GY6W6e2IPemfvGZeUMqZ9A/3GqIZMgKnBhAw/Q==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

escape-string-regexp@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz#4683126b500b61762f2dbebace1806e8be31b1c8"
  integrity sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

esniff@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/esniff/-/esniff-1.1.0.tgz"
  integrity sha512-vmHXOeOt7FJLsqofvFk4WB3ejvcHizCd8toXXwADmYfd02p2QwHRgkUbhYDX54y08nqk818CUTWipgZGlyN07g==
  dependencies:
    d "1"
    es5-ext "^0.10.12"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

essentials@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/essentials/-/essentials-1.2.0.tgz"
  integrity sha512-kP/j7Iw7KeNE8b/o7+tr9uX2s1wegElGOoGZ2Xm35qBr4BbbEcH3/bxR2nfH9l9JANCq9AUrvKw+gRuHtZp0HQ==
  dependencies:
    uni-global "^1.0.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-emitter@^0.3.5, event-emitter@~0.3.5:
  version "0.3.5"
  resolved "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz"
  integrity sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==
  dependencies:
    d "1"
    es5-ext "~0.10.14"

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/events/-/events-1.1.1.tgz"
  integrity sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==

events@^3.2.0, events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/execa/-/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
  integrity sha512-RztN09XglpYI7aBBrJCPW95jEH7YF1UEPOoX9yDhUTPdp7mK+CQvnLTuD10BNXZ3byLTu2uehZ8EcKT/4CGiFw==
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/execa/-/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

executable@^4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/executable/-/executable-4.1.1.tgz#41532bff361d3e57af4d763b70582db18f5d133c"
  integrity sha512-8iA79xD3uAch729dUG8xaaBBFGaEa0wdD2VkYLFHwlqosEj/jT66AzcreRDSgV7ehnNLBW2WR5jIXwGKjVdTLg==
  dependencies:
    pify "^2.2.0"

expose-loader@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/expose-loader/-/expose-loader-3.1.0.tgz"
  integrity sha512-2RExSo0yJiqP+xiUue13jQa2IHE8kLDzTI7b6kn+vUlBVvlzNSiLDzo4e5Pp5J039usvTUnxZ8sUOhv0Kg15NA==

express-session@^1.17.2:
  version "1.17.2"
  resolved "https://registry.npmjs.org/express-session/-/express-session-1.17.2.tgz"
  integrity sha512-mPcYcLA0lvh7D4Oqr5aNJFMtBMKPLl++OKKxkHzZ0U0oDq1rpKBnkR5f5vCHR26VeArlTOEF9td4x5IjICksRQ==
  dependencies:
    cookie "0.4.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~2.0.0"
    on-headers "~1.0.2"
    parseurl "~1.3.3"
    safe-buffer "5.2.1"
    uid-safe "~2.1.5"

express@^4.17.2:
  version "4.18.1"
  resolved "https://registry.npmjs.org/express/-/express-4.18.1.tgz"
  integrity sha512-zZBcOX9TfehHQhtupq57OF8lFZ3UZi08Y97dwFCkD8p9d/d2Y3M+ykKcwaMDEL+4qyUolgBDX6AblpR3fL212Q==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.0"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.10.3"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext-list@^2.0.0:
  version "2.2.2"
  resolved "https://registry.npmjs.org/ext-list/-/ext-list-2.2.2.tgz"
  integrity sha512-u+SQgsubraE6zItfVA0tBuCBhfU9ogSRnsvygI7wht9TS510oLkBRXBsqopeUG/GBOIQyKZO9wjTqIu/sf5zFA==
  dependencies:
    mime-db "^1.28.0"

ext-name@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/ext-name/-/ext-name-5.0.0.tgz"
  integrity sha512-yblEwXAbGv1VQDmow7s38W77hzAgJAO50ztBLMcUyUBfxv1HC+LGwtiEN+Co6LtlqT/5uwVOxsD4TNIilWhwdQ==
  dependencies:
    ext-list "^2.0.0"
    sort-keys-length "^1.0.0"

ext@^1.1.2, ext@^1.4.0, ext@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/ext/-/ext-1.6.0.tgz"
  integrity sha512-sdBImtzkq2HpkdRLtlLWDa6w4DX22ijZLKx8BMPUuKe1c5lbN6xwQDQCxSfxBQnHZ13ls/FH0MQZx/q/gr6FQg==
  dependencies:
    type "^2.5.0"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.2.5:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/fast-glob/-/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.2.11"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz"
  integrity sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-safe-stringify@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
  integrity sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==

fast-xml-parser@3.19.0:
  version "3.19.0"
  resolved "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-3.19.0.tgz"
  integrity sha512-4pXwmBplsCPv8FOY1WRakF970TjNGnGnfbOnLqjlYvMiF1SR3yOHyxMR/YCXpPTOspNF5gwudqktIP4VsWkvBg==

fastest-levenshtein@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.12.tgz"
  integrity sha512-On2N+BpYJ15xIC974QNVuYGMOlEVt4s0EOI3wwMqOmK1fdDY+FN/zltPV8vosq4ad4c/gJ1KHScUn/6AWIgiow==

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
  integrity sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==
  dependencies:
    reusify "^1.0.4"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz"
  integrity sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==
  dependencies:
    pend "~1.2.0"

feathers-mongoose@^8.5.1:
  version "8.5.1"
  resolved "https://registry.npmjs.org/feathers-mongoose/-/feathers-mongoose-8.5.1.tgz"
  integrity sha512-ZP2Ztt+UIRKD+6Qyr8ajPPhhoi3Kq+7oBPaxUeXlNmxkF7C0N3/rZhF3cuk3OdQgGLMqIVqewhlL7va380NU/w==
  dependencies:
    "@feathersjs/adapter-commons" "^4.5.11"
    "@feathersjs/commons" "^4.5.11"
    "@feathersjs/errors" "^4.5.11"

fecha@^4.2.0:
  version "4.2.3"
  resolved "https://registry.npmjs.org/fecha/-/fecha-4.2.3.tgz"
  integrity sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz"
  integrity sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==
  dependencies:
    escape-string-regexp "^1.0.5"

file-type@^16.5.3:
  version "16.5.3"
  resolved "https://registry.npmjs.org/file-type/-/file-type-16.5.3.tgz"
  integrity sha512-uVsl7iFhHSOY4bEONLlTK47iAHtNsFHWP5YE4xJfZ4rnX7S1Q3wce09XgqSC7E/xh8Ncv/be1lNoyprlUH/x6A==
  dependencies:
    readable-web-to-node-stream "^3.0.0"
    strtok3 "^6.2.4"
    token-types "^4.1.1"

file-type@^17.1.6:
  version "17.1.6"
  resolved "https://registry.yarnpkg.com/file-type/-/file-type-17.1.6.tgz#18669e0577a4849ef6e73a41f8bdf1ab5ae21023"
  integrity sha512-hlDw5Ev+9e883s0pwUsuuYNu4tD7GgpUnOvykjv1Gya0ZIjuKumthDRua90VUn6/nlRKAjcxLUnHNTIUWwWIiw==
  dependencies:
    readable-web-to-node-stream "^3.0.2"
    strtok3 "^7.0.0-alpha.9"
    token-types "^5.0.0-alpha.2"

file-type@^3.3.0, file-type@^3.8.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/file-type/-/file-type-3.9.0.tgz"
  integrity sha512-RLoqTXE8/vPmMuTI88DAzhMYC99I8BWv7zYP4A1puo5HIjEJ5EX48ighy4ZyKMG9EDXxBgW6e++cn7d1xuFghA==

file-type@^4.2.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/file-type/-/file-type-4.4.0.tgz"
  integrity sha512-f2UbFQEk7LXgWpi5ntcO86OeA/cC80fuDDDaX/fZ2ZGel+AF7leRQqBBW1eJNiiQkrZlAoM6P+VYP5P6bOlDEQ==

file-type@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/file-type/-/file-type-5.2.0.tgz"
  integrity sha512-Iq1nJ6D2+yIO4c8HHg4fyVb8mAJieo1Oloy1mLLaB2PvezNedhBVm+QU7g0qM42aiMbRXTxKKwGD17rjKNJYVQ==

file-type@^6.1.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/file-type/-/file-type-6.2.0.tgz"
  integrity sha512-YPcTBDV+2Tm0VqjybVd32MHdlEGAtuxS3VAYsumFokDSMG+ROT5wawGlnHDoz7bfMcMDt9hxuXvXwoKUx2fkOg==

filename-reserved-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/filename-reserved-regex/-/filename-reserved-regex-2.0.0.tgz"
  integrity sha512-lc1bnsSr4L4Bdif8Xb/qrtokGbq5zlsms/CYH8PP+WtCkGNF65DPiQY8vG3SakEdRn8Dlnm+gW/qWKKjS5sZzQ==

filename-reserved-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/filename-reserved-regex/-/filename-reserved-regex-3.0.0.tgz#3d5dd6d4e2d73a3fed2ebc4cd0b3448869a081f7"
  integrity sha512-hn4cQfU6GOT/7cFHXBqeBg2TbrMBgdD0kcjLhvSQYYwm3s4B6cjvBfb7nBALJLAXqmU5xajSa7X2NnUud/VCdw==

filenamify@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/filenamify/-/filenamify-4.3.0.tgz"
  integrity sha512-hcFKyUG57yWGAzu1CMt/dPzYZuv+jAJUT85bL8mrXvNe6hWj6yEHEc4EdcgiA6Z3oi1/9wXJdZPXF2dZNgwgOg==
  dependencies:
    filename-reserved-regex "^2.0.0"
    strip-outer "^1.0.1"
    trim-repeated "^1.0.0"

filenamify@^5.0.2:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/filenamify/-/filenamify-5.1.1.tgz#a1ccc5ae678a5e34f578afcb9b72898264d166d2"
  integrity sha512-M45CbrJLGACfrPOkrTp3j2EcO9OBkKUYME0eiqOCa7i2poaklU0jhlIaMlr8ijLorT0uLAzrn3qXOp5684CkfA==
  dependencies:
    filename-reserved-regex "^3.0.0"
    strip-outer "^2.0.0"
    trim-repeated "^2.0.0"

filesize@^8.0.7:
  version "8.0.7"
  resolved "https://registry.npmjs.org/filesize/-/filesize-8.0.7.tgz"
  integrity sha512-pjmC+bkIF8XI7fWaH8KxHcZL3DPybs1roSKP4rKDvy20tAWwIObE4+JIseG2byfGKhud5ZnM4YSGKBz7Sh0ndQ==

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-requires@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/find-requires/-/find-requires-1.0.0.tgz"
  integrity sha512-UME7hNwBfzeISSFQcBEDemEEskpOjI/shPrpJM5PI4DSdn6hX0dmz+2dL70blZER2z8tSnTRL+2rfzlYgtbBoQ==
  dependencies:
    es5-ext "^0.10.49"
    esniff "^1.1.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-versions@^5.0.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/find-versions/-/find-versions-5.1.0.tgz#973f6739ce20f5e439a27eba8542a4b236c8e685"
  integrity sha512-+iwzCJ7C5v5KgcBuueqVoNiHVoQpwiUK5XFLjf0affFTep+Wcw93tPvmb8tqujDNmzhBDPddnWV/qgWSXgq+Hg==
  dependencies:
    semver-regex "^4.0.5"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz"
  integrity sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==

fn.name@1.x.x:
  version "1.1.0"
  resolved "https://registry.npmjs.org/fn.name/-/fn.name-1.1.0.tgz"
  integrity sha512-GRnmB5gPyJpAhTQdSZTSp9uaPSvl09KoYcMQtsB9rQoOmzs9dH6ffeccH+Z+cv6P68Hu5bC6JjRh4Ah/mHSNRw==

follow-redirects@^1.14.0, follow-redirects@^1.14.9:
  version "1.15.1"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.1.tgz"
  integrity sha512-yLAMQs+k0b2m7cVxpS1VKJVvoz7SS9Td1zss3XRwXj+ZDH00RJgnuLx7E44wx02kQLrdM3aOOy+FpzS7+8OizA==

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formidable@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/formidable/-/formidable-2.0.1.tgz"
  integrity sha512-rjTMNbp2BpfQShhFbR3Ruk3qk2y9jKpvMW78nJgx8QKtxjDVrwbZG+wvDOmVbifHyOUOQJXxqEy6r0faRrPzTQ==
  dependencies:
    dezalgo "1.0.3"
    hexoid "1.0.0"
    once "1.4.0"
    qs "6.9.3"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz"
  integrity sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^9.1.0:
  version "9.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz"
  integrity sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^2.0.0, fs-minipass@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fs2@^0.3.9:
  version "0.3.9"
  resolved "https://registry.npmjs.org/fs2/-/fs2-0.3.9.tgz"
  integrity sha512-WsOqncODWRlkjwll+73bAxVW3JPChDgaPX3DT4iTTm73UmG4VgALa7LaFblP232/DN60itkOrPZ8kaP1feksGQ==
  dependencies:
    d "^1.0.1"
    deferred "^0.7.11"
    es5-ext "^0.10.53"
    event-emitter "^0.3.5"
    ignore "^5.1.8"
    memoizee "^0.4.14"
    type "^2.1.0"

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

gauge@^4.0.3:
  version "4.0.4"
  resolved "https://registry.npmjs.org/gauge/-/gauge-4.0.4.tgz"
  integrity sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.3"
    console-control-strings "^1.1.0"
    has-unicode "^2.0.1"
    signal-exit "^3.0.7"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.5"

generic-pool@3.8.2:
  version "3.8.2"
  resolved "https://registry.npmjs.org/generic-pool/-/generic-pool-3.8.2.tgz"
  integrity sha512-nGToKy6p3PAbYQ7p1UlWl6vSPwfwU6TMSWK7TTu+WUY4ZjyZQGniGGt2oNVvyNSpyZYSB43zMXVLcBm08MTMkg==

get-intrinsic@^1.0.2:
  version "1.1.1"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  integrity sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-stdin@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/get-stdin/-/get-stdin-8.0.0.tgz"
  integrity sha512-sY22aA6xchAzprjyqmSEQv4UbAAzRN0L2dQB0NlN5acTTK9Don6nhoc3eAbUnpZiCANAMfd/+40kVdKfFygohg==

get-stream@^2.2.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-2.3.1.tgz"
  integrity sha512-AUGhbbemXxrZJRD5cDvKtQxLuYaIbNtDTK8YqupCI393Q2KSTreEsLUN3ZxAWFGiKTzL6nKuzfcIvieflUX9qA==
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==

get-stream@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
  dependencies:
    pump "^3.0.0"

get-stream@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz"
  integrity sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0, get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^7.0.0, glob@^7.0.5, glob@^7.1.3, glob@^7.1.4, glob@^7.2.0:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^8.0.1:
  version "8.0.3"
  resolved "https://registry.npmjs.org/glob/-/glob-8.0.3.tgz"
  integrity sha512-ull455NHSHI/Y1FqGaaYFaLGkNMMJbavMrEGFXG/PGrg6y7sutWHUHrz6gy6WEBH6akM1M414dWKCNs+IhKdiQ==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^5.0.1"
    once "^1.3.0"

global-dirs@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/global-dirs/-/global-dirs-3.0.0.tgz"
  integrity sha512-v8ho2DS5RiCjftj1nD9NmnfaOzTdud7RRnVd9kFNOjqZbISlx5DQ+OrTkywgd0dIt7oFCvKetZSHoHcP3sDdiA==
  dependencies:
    ini "2.0.0"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

got@^11.8.3, got@^11.8.5:
  version "11.8.5"
  resolved "https://registry.npmjs.org/got/-/got-11.8.5.tgz"
  integrity sha512-o0Je4NvQObAuZPHLFoRSkdG2lTgtcynqymzg2Vupdx6PorhaT5MCbIyXG6d4D94kk8ZG57QeosgdiqfJWhEhlQ==
  dependencies:
    "@sindresorhus/is" "^4.0.0"
    "@szmarczak/http-timer" "^4.0.5"
    "@types/cacheable-request" "^6.0.1"
    "@types/responselike" "^1.0.0"
    cacheable-lookup "^5.0.3"
    cacheable-request "^7.0.2"
    decompress-response "^6.0.0"
    http2-wrapper "^1.0.0-beta.5.2"
    lowercase-keys "^2.0.0"
    p-cancelable "^2.0.0"
    responselike "^2.0.0"

got@^9.6.0:
  version "9.6.0"
  resolved "https://registry.npmjs.org/got/-/got-9.6.0.tgz"
  integrity sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==
  dependencies:
    "@sindresorhus/is" "^0.14.0"
    "@szmarczak/http-timer" "^1.1.2"
    cacheable-request "^6.0.0"
    decompress-response "^3.3.0"
    duplexer3 "^0.1.4"
    get-stream "^4.1.0"
    lowercase-keys "^1.0.1"
    mimic-response "^1.0.1"
    p-cancelable "^1.0.0"
    to-readable-stream "^1.0.0"
    url-parse-lax "^3.0.0"

graceful-fs@^4.1.10, graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.10, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.10"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz"
  integrity sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==

grant-profile@^0.0.11:
  version "0.0.11"
  resolved "https://registry.npmjs.org/grant-profile/-/grant-profile-0.0.11.tgz"
  integrity sha512-gfSFCZC2zmwkWYQ3rb2J6JbINbgExQgkjssOMJrKg1nq9izjP0Yzj5F0HzQuRUMi84onAo7dQJN4ZDVYXzavhw==
  dependencies:
    qs "^6.9.1"
    request-compose "^1.2.1"
    request-oauth "0.0.3"

grant@^4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/grant/-/grant-4.7.0.tgz"
  integrity sha512-QGPjCYDrBnb/OIiTRxbK3TnNOE6Ycgfc/GcgPzI4vyNIr+b7yisEexYp7VM74zj6bxr+mDTzfGONRLzzsVPJIA==
  dependencies:
    qs "^6.9.1"
    request-compose "^1.2.1"
    request-oauth "0.0.3"

graphlib@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmjs.org/graphlib/-/graphlib-2.1.8.tgz"
  integrity sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==
  dependencies:
    lodash "^4.17.15"

handlebars@^4.7.7:
  version "4.7.7"
  resolved "https://registry.npmjs.org/handlebars/-/handlebars-4.7.7.tgz"
  integrity sha512-aAcXm5OAfE/8IXkcZvCepKU3VzW1/39Fb5ZuqMtgI/hT8X2YgoMvBY5dLhq/cpOvw7Lk1nK/UF71aLG/ZnVYRA==
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.0"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

has-binary2@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-binary2/-/has-binary2-1.0.3.tgz"
  integrity sha512-G1LWKhDSvhGeAQ8mPVQlqNcOB2sJdwATtZKl2pDKKHfpf/rYj24lkinxf69blJbnsvtqqNU+L3SL50vzZhXOnw==
  dependencies:
    isarray "2.0.1"

has-cors@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-cors/-/has-cors-1.1.0.tgz"
  integrity sha512-g5VNKdkFuUuVCP9gYfDJHjK2nqdQJ7aDLTnycnc2+RvsOQbuLdF5pm7vuE5J76SEBIQjs4kQY/BWq74JUmjbXA==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-symbols@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

has-yarn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/has-yarn/-/has-yarn-2.1.0.tgz"
  integrity sha512-UqBRqi4ju7T+TqGNdqAO0PaSVGsDGJUBQvk9eUWNGRY1CFGDzYhLWoM7JQEemnlvVcv/YEmc2wNW8BC24EnUsw==

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

helmet@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/helmet/-/helmet-4.6.0.tgz"
  integrity sha512-HVqALKZlR95ROkrnesdhbbZJFi/rIVSoNq6f3jA/9u6MIbTsPh3xZwihjeI5+DO/2sOV6HMHooXcEOuwskHpTg==

hexoid@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hexoid/-/hexoid-1.0.0.tgz"
  integrity sha512-QFLV0taWQOZtvIRIAdBChesmogZrtuXvVWsFHZTk2SU+anspqZ2vMnoLg7IE1+Uk16N19APic1BuF8bC8c2m5g==

hosted-git-info@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-5.0.0.tgz"
  integrity sha512-rRnjWu0Bxj+nIfUOkz0695C0H6tRrN5iYIzYejb0tDEefe2AekHu/U5Kn9pEie5vsJqpNQU02az7TGSH3qpz4Q==
  dependencies:
    lru-cache "^7.5.1"

html-comment-regex@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.2.tgz"
  integrity sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ==

http-cache-semantics@^4.0.0, http-cache-semantics@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.0.tgz"
  integrity sha512-carPklcUh7ROWRK7Cv27RPtdhYhUsela/ue5/jKzjegVvXDqM2ILE9Q2BGn9JZJh1g87cp56su/FgQSzcWS8cQ==

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
  integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
  dependencies:
    "@tootallnate/once" "2"
    agent-base "6"
    debug "4"

http2-wrapper@^1.0.0-beta.5.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.3.tgz"
  integrity sha512-V+23sDMr12Wnz7iTcDeJr3O6AIxlnvT/bmaAAAP/Xda35C90p9599p0F1eHR/N1KILWSoWVAiOMFjBBXaXSMxg==
  dependencies:
    quick-lru "^5.1.1"
    resolve-alpn "^1.0.0"

https-proxy-agent@^5.0.0, https-proxy-agent@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/human-signals/-/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz"
  integrity sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==
  dependencies:
    ms "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@1.1.13, ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore-by-default@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz"
  integrity sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==

ignore-walk@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ignore-walk/-/ignore-walk-5.0.1.tgz"
  integrity sha512-yemi4pMf51WKT7khInJqAvsIGzoqYXblnsz0ql8tM+yi1EKYTY1evX4NAbJrLL/Aanr2HyZeluqU+Oi7MGHokw==
  dependencies:
    minimatch "^5.0.1"

ignore@^5.1.8, ignore@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz"
  integrity sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  integrity sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==

import-lazy@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/import-lazy/-/import-lazy-2.1.0.tgz"
  integrity sha512-m7ZEHgtw69qOGw+jwxXkHlrlIPdTGkyh66zXZ1ajZbxkDBNjSY/LGbmjc7h0s2ELsUDTAhFr55TrPSSqJGPG0A==

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz"
  integrity sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz"
  integrity sha512-i0G7hLJ1z0DE8dsqJa2rycj9dBmNKgXBvotXtZYXakU9oivfB9Uj2ZBC27qqef2U58/ZLwalxa1X/RDCdkHtVg==

infer-owner@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/infer-owner/-/infer-owner-1.0.4.tgz"
  integrity sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  integrity sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==

ini@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ini/-/ini-2.0.0.tgz"
  integrity sha512-7PnF4oN3CvZF23ADhA5wRaYEQpJ8qygSkbtTXWBeXWXmEVRXK+1ITciHWwHhsjv1TmW0MgacIv6hEi5pX5NQdA==

ini@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/ini/-/ini-3.0.0.tgz"
  integrity sha512-TxYQaeNW/N8ymDvwAxPyRbhMBtnEwuvaTYpOQkFx1nSeusgezHniEc/l35Vo4iCq/mMiTJbpD7oYxN98hFlfmw==

ini@~1.3.0:
  version "1.3.8"
  resolved "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz"
  integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==

init-package-json@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/init-package-json/-/init-package-json-3.0.2.tgz"
  integrity sha512-YhlQPEjNFqlGdzrBfDNRLhvoSgX7iQRgSxgsNknRQ9ITXFT7UMfVMWhBTOh2Y+25lRnGrv5Xz8yZwQ3ACR6T3A==
  dependencies:
    npm-package-arg "^9.0.1"
    promzard "^0.3.0"
    read "^1.0.7"
    read-package-json "^5.0.0"
    semver "^7.3.5"
    validate-npm-package-license "^3.0.4"
    validate-npm-package-name "^4.0.0"

inquirer@^8.2.4:
  version "8.2.4"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-8.2.4.tgz"
  integrity sha512-nn4F01dxU8VeKfq192IjLsxu0/OmMZ4Lg3xKAns148rCaXP6ntAoEkVYZThWjwON8AlzdZZi6oqnhNbxUG9hVg==
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^7.0.0"

install@^0.13.0:
  version "0.13.0"
  resolved "https://registry.npmjs.org/install/-/install-0.13.0.tgz"
  integrity sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz"
  integrity sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==

interpret@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/interpret/-/interpret-2.2.0.tgz"
  integrity sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==

ip-regex@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ip-regex/-/ip-regex-4.3.0.tgz"
  integrity sha512-B9ZWJxHHOHUhUjCPrMpLD4xEq35bUTClHM1S6CBU5ixQnkZmwipwgc96vAd7AAGM9TGHvJR+Uss+/Ak6UphK+Q==

ip@^1.1.5:
  version "1.1.8"
  resolved "https://registry.npmjs.org/ip/-/ip-1.1.8.tgz"
  integrity sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz"
  integrity sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==
  dependencies:
    ci-info "^2.0.0"

is-cidr@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/is-cidr/-/is-cidr-4.0.2.tgz"
  integrity sha512-z4a1ENUajDbEl/Q6/pVBpTR1nBjjEE1X7qb7bmWYanNnPoKAvUCPFKeXV6Fe4mgTkWKBqiHIcwsI3SndiO5FeA==
  dependencies:
    cidr-regex "^3.1.1"

is-core-module@^2.8.1, is-core-module@^2.9.0:
  version "2.9.0"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.9.0.tgz"
  integrity sha512-+5FPy5PnwmO3lvfMb0AsoPaBG+5KHUI0wYFXOtYPnVVVspTFUuMZNfNaNVRt3FZadstu2c8x23vykRW/NBoU6A==
  dependencies:
    has "^1.0.3"

is-docker@^2.0.0, is-docker@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-installed-globally@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/is-installed-globally/-/is-installed-globally-0.4.0.tgz"
  integrity sha512-iwGqO3J21aaSkC7jWnHP/difazwS7SFeIqxv6wEtLU8Y5KlzFTjyqcSIT0d8s4+dDhKytsk9PJZ2BkS5eZwQRQ==
  dependencies:
    global-dirs "^3.0.0"
    is-path-inside "^3.0.2"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==

is-lambda@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-lambda/-/is-lambda-1.0.1.tgz"
  integrity sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==

is-natural-number@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/is-natural-number/-/is-natural-number-4.0.1.tgz"
  integrity sha512-Y4LTamMe0DDQIIAlaer9eKebAlDSV6huy+TWhJVPlzZh2o4tRP5SQWFlLn5N0To4mDD22/qdOq+veo1cSISLgQ==

is-npm@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/is-npm/-/is-npm-5.0.0.tgz"
  integrity sha512-WW/rQLOazUq+ST/bCAVBp/2oMERWLsR7OrKyt052dNDk4DHcDE0/7QSXITlmi+VBcV13DfIbysG3tZJm5RfdBA==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz"
  integrity sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==

is-path-inside@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  integrity sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==
  dependencies:
    isobject "^3.0.1"

is-promise@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmjs.org/is-promise/-/is-promise-2.2.2.tgz"
  integrity sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  integrity sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  integrity sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"
  integrity sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q==

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

is-yarn-global@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/is-yarn-global/-/is-yarn-global-0.3.0.tgz"
  integrity sha512-VjSeb/lHmkoyd8ryPVIKvOCn4D1koMqY+vqyjjUfc3xyKtP4dYOxM44sZrnqQSzSds3xyOrUTLTC9LVCVgLngw==

isarray@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.1.tgz"
  integrity sha512-c2cu3UxbI+b6kR3fy0nRnAhodsvR9dx7U5+znCOzdj6IfP3upFURTr0Xl5BlQZNKZjEtxrmVyfSdeE3O57smoQ==

isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  integrity sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==

isomorphic-ws@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-4.0.1.tgz"
  integrity sha512-BhBvN2MBpWTaSHdWRb/bwdZJ1WaehQ2L1KngkCkfLUGF0mAWAT1sQUQacEmQ0jXkFw/czDXPNQSL5u2/Krsz1w==

jest-diff@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-diff/-/jest-diff-27.5.1.tgz"
  integrity sha512-m0NvkX55LDt9T4mctTEgnZk3fmEg3NRYutvMPWM/0iPnkFj2wIeF45O1718cMSOFO1vINkqmxqD8vE37uTEbqw==
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^27.5.1"
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-get-type@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.5.1.tgz"
  integrity sha512-2KY95ksYSaK7DMBWQn6dQz3kqAf3BB64y2udeG+hv4KfSOb9qwcYQstTJc1KCbsix+wLZWZYN8t7nwX3GOBLRw==

jest-matcher-utils@^27.0.0:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-27.5.1.tgz"
  integrity sha512-z2uTx/T6LBaCoNWNFWwChLBKYxTMcGBRjAt+2SbP929/Fflb9aa5LGma654Rz8z9HLxsrUaYzxE9T/EFIL/PAw==
  dependencies:
    chalk "^4.0.0"
    jest-diff "^27.5.1"
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://registry.npmjs.org/jmespath/-/jmespath-0.16.0.tgz"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

joycon@^3.0.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/joycon/-/joycon-3.1.1.tgz"
  integrity sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==

jquery@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/jquery/-/jquery-3.6.0.tgz"
  integrity sha512-JVzAR/AjBvVt2BmYhxRCSYysDsPcssdmTFnzyLEts9qNwmjmu4JTAMYubEfwVOSwpQ1I1sKKFcxhZCI2buerfw==

js-yaml@^3.13.1, js-yaml@^3.14.1:
  version "3.14.1"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

json-buffer@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.0.tgz"
  integrity sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ==

json-buffer@3.0.1, json-buffer@~3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-cycle@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/json-cycle/-/json-cycle-1.3.0.tgz"
  integrity sha512-FD/SedD78LCdSvJaOUQAXseT8oQBb5z6IVYaQaCrVUlu9zOAr1BDdKyVYQaSD/GDsAMrXpKcOyBD4LIl8nfjHw==

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-refs@^3.0.15:
  version "3.0.15"
  resolved "https://registry.npmjs.org/json-refs/-/json-refs-3.0.15.tgz"
  integrity sha512-0vOQd9eLNBL18EGl5yYaO44GhixmImes2wiYn9Z3sag3QnehWrYWlB9AFtMxCL2Bj3fyxgDYkxGFEU/chlYssw==
  dependencies:
    commander "~4.1.1"
    graphlib "^2.1.8"
    js-yaml "^3.13.1"
    lodash "^4.17.15"
    native-promise-only "^0.8.1"
    path-loader "^1.0.10"
    slash "^3.0.0"
    uri-js "^4.2.2"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json-stringify-nice@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/json-stringify-nice/-/json-stringify-nice-1.1.4.tgz"
  integrity sha512-5Z5RFW63yxReJ7vANgW6eZFGWaQvnPE3WNmZoOJrSkGju2etKA2L5rrOa1sm877TVTFt57A80BH1bArcmlLfPw==

json5@^2.1.1, json5@^2.1.2, json5@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz"
  integrity sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz"
  integrity sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==

jsonwebtoken@^8.5.1:
  version "8.5.1"
  resolved "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.5.1.tgz"
  integrity sha512-XjwVfRS6jTMsqYs0EsuJ4LGxXV14zQybNd4L2r0UvbVnSF9Af8x7p5MzbJ90Ioz/9TI41/hTCvznF/loiSzn8w==
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^5.6.0"

jszip@^3.9.1:
  version "3.10.0"
  resolved "https://registry.npmjs.org/jszip/-/jszip-3.10.0.tgz"
  integrity sha512-LDfVtOLtOxb9RXkYOwPyNBTQDL4eUbqahtoY6x07GiDJHwSYvn8sHHIw8wINImV3MqbMNve2gSuM1DDqEKk09Q==
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

just-diff-apply@^5.2.0:
  version "5.4.1"
  resolved "https://registry.yarnpkg.com/just-diff-apply/-/just-diff-apply-5.4.1.tgz#1debed059ad009863b4db0e8d8f333d743cdd83b"
  integrity sha512-AAV5Jw7tsniWwih8Ly3fXxEZ06y+6p5TwQMsw0dzZ/wPKilzyDgdAnL0Ug4NNIquPUOh1vfFWEHbmXUqM5+o8g==

just-diff@^5.0.1:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/just-diff/-/just-diff-5.1.1.tgz#8da6414342a5ed6d02ccd64f5586cbbed3146202"
  integrity sha512-u8HXJ3HlNrTzY7zrYYKjNEfBlyjqhdBkoyTVdjtn7p02RJD5NvR8rIClzeGA7t+UYP1/7eAkWNLU0+P3QrEqKQ==

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz"
  integrity sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  integrity sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

jwt-decode@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/jwt-decode/-/jwt-decode-2.2.0.tgz"
  integrity sha512-86GgN2vzfUu7m9Wcj63iUkuDzFNYFVmjeDm2GzWpUk+opB0pEpMsw6ePCMrhYkumz2C1ihqtZzOMAg7FiXcNoQ==

jwt-decode@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/jwt-decode/-/jwt-decode-3.1.2.tgz"
  integrity sha512-UfpWE/VZn0iP50d8cz9NrZLM9lSWhcJ+0Gt/nm4by88UL+J1SiKN8/5dkjMmbEzwL2CAe+67GsegCbIKtbp75A==

kareem@2.3.5:
  version "2.3.5"
  resolved "https://registry.npmjs.org/kareem/-/kareem-2.3.5.tgz"
  integrity sha512-qxCyQtp3ioawkiRNQr/v8xw9KIviMSSNmy+63Wubj7KmMn3g7noRXIZB4vPCAP+ETi2SR8eH6CvmlKZuGpoHOg==

keyv@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/keyv/-/keyv-3.1.0.tgz"
  integrity sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==
  dependencies:
    json-buffer "3.0.0"

keyv@^4.0.0:
  version "4.3.2"
  resolved "https://registry.npmjs.org/keyv/-/keyv-4.3.2.tgz"
  integrity sha512-kn8WmodVBe12lmHpA6W8OY7SNh6wVR+Z+wZESF4iF5FCazaVXGWOtnbnvX0tMQ1bO+/TmOD9LziuYMvrIIs0xw==
  dependencies:
    compress-brotli "^1.3.8"
    json-buffer "3.0.1"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

klaw-sync@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/klaw-sync/-/klaw-sync-6.0.0.tgz"
  integrity sha512-nIeuVSzdCCs6TDPTqI8w1Yre34sSq7AkZ4B3sfOBbI2CgVSB4Du4aLQijFU2+lhAFCwt9+42Hel6lQNIv6AntQ==
  dependencies:
    graceful-fs "^4.1.11"

kuler@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/kuler/-/kuler-2.0.0.tgz"
  integrity sha512-Xq9nH7KlWZmXAtodXDDRE7vs6DU1gTU8zYDHDiWLSip45Egwq3plLHzPn27NgvzL2r1LMPC1vdqh98sQxtqj4A==

latest-version@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/latest-version/-/latest-version-5.1.0.tgz"
  integrity sha512-weT+r0kTkRQdCdYCNtkMwWXQTMEswKrFBkm4ckQOMVhhqhIMI1UT2hMj+1iigIhgSZm5gTmrRXBNoGUgaTY1xA==
  dependencies:
    package-json "^6.3.0"

lazystream@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/lazystream/-/lazystream-1.0.1.tgz"
  integrity sha512-b94GiNHQNy6JNTrt5w6zNyffMrNkXZb3KTkCZJb2V1xaEGCk093vkZ2jk3tpaeP33/OiXC+WvK9AxUebnf5nbw==
  dependencies:
    readable-stream "^2.0.5"

libnpmaccess@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmjs.org/libnpmaccess/-/libnpmaccess-6.0.3.tgz"
  integrity sha512-4tkfUZprwvih2VUZYMozL7EMKgQ5q9VW2NtRyxWtQWlkLTAWHRklcAvBN49CVqEkhUw7vTX2fNgB5LzgUucgYg==
  dependencies:
    aproba "^2.0.0"
    minipass "^3.1.1"
    npm-package-arg "^9.0.1"
    npm-registry-fetch "^13.0.0"

libnpmdiff@^4.0.2:
  version "4.0.4"
  resolved "https://registry.npmjs.org/libnpmdiff/-/libnpmdiff-4.0.4.tgz"
  integrity sha512-bUz12309DdkeFL/K0sKhW1mbg8DARMbNI0vQKrJp1J8lxhxqkAjzSQ3eQCacFjSwCz4xaf630ogwuOkSt61ZEQ==
  dependencies:
    "@npmcli/disparity-colors" "^2.0.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    binary-extensions "^2.2.0"
    diff "^5.0.0"
    minimatch "^5.0.1"
    npm-package-arg "^9.0.1"
    pacote "^13.6.1"
    tar "^6.1.0"

libnpmexec@^4.0.2:
  version "4.0.8"
  resolved "https://registry.npmjs.org/libnpmexec/-/libnpmexec-4.0.8.tgz"
  integrity sha512-SKO6JCt/rL6r+ilbq315zEj2sDdZRniCJ2AvmzqMyIKW4IMuuLsOjjkcWKBV2l1Vle54ud7Tkv9IEPR2cE0mJg==
  dependencies:
    "@npmcli/arborist" "^5.0.0"
    "@npmcli/ci-detect" "^2.0.0"
    "@npmcli/run-script" "^4.1.3"
    chalk "^4.1.0"
    mkdirp-infer-owner "^2.0.0"
    npm-package-arg "^9.0.1"
    npmlog "^6.0.2"
    pacote "^13.6.1"
    proc-log "^2.0.0"
    read "^1.0.7"
    read-package-json-fast "^2.0.2"
    walk-up-path "^1.0.0"

libnpmfund@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmjs.org/libnpmfund/-/libnpmfund-3.0.2.tgz"
  integrity sha512-wmFMP/93Wjy+jDg5LaSldDgAhSgCyA64JUUmp806Kae7y3YP9Qv5m1vUhPxT4yebxgB2v/I6G1/RUcNb1y0kVg==
  dependencies:
    "@npmcli/arborist" "^5.0.0"

libnpmhook@^8.0.2:
  version "8.0.3"
  resolved "https://registry.npmjs.org/libnpmhook/-/libnpmhook-8.0.3.tgz"
  integrity sha512-TEdNI1mC5zS+w/juCgxrwwQnpbq9lY76NDOS0N37pn6pWIUxB1Yq8mwy6MUEXR1TgH4HurSQyKT6I6Kp9Wjm4A==
  dependencies:
    aproba "^2.0.0"
    npm-registry-fetch "^13.0.0"

libnpmorg@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmjs.org/libnpmorg/-/libnpmorg-4.0.3.tgz"
  integrity sha512-r4CpmCEF+e5PbFMBi64xSXmqn0uGgV4T7NWpGL4/A6KT/DTtIxALILQZq+l0ZdN1xm4RjOvqSDR22oT4il8rAQ==
  dependencies:
    aproba "^2.0.0"
    npm-registry-fetch "^13.0.0"

libnpmpack@^4.0.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/libnpmpack/-/libnpmpack-4.1.2.tgz"
  integrity sha512-megSAPeZGv9jnDM4KovKbczjyuy/EcPxCIU/iaWsDU1IEAVtBJ0qHqNUm5yN2AgN501Tb3CL6KeFGYdG4E31rQ==
  dependencies:
    "@npmcli/run-script" "^4.1.3"
    npm-package-arg "^9.0.1"
    pacote "^13.6.1"

libnpmpublish@^6.0.2:
  version "6.0.4"
  resolved "https://registry.npmjs.org/libnpmpublish/-/libnpmpublish-6.0.4.tgz"
  integrity sha512-lvAEYW8mB8QblL6Q/PI/wMzKNvIrF7Kpujf/4fGS/32a2i3jzUXi04TNyIBcK6dQJ34IgywfaKGh+Jq4HYPFmg==
  dependencies:
    normalize-package-data "^4.0.0"
    npm-package-arg "^9.0.1"
    npm-registry-fetch "^13.0.0"
    semver "^7.3.7"
    ssri "^9.0.0"

libnpmsearch@^5.0.2:
  version "5.0.3"
  resolved "https://registry.npmjs.org/libnpmsearch/-/libnpmsearch-5.0.3.tgz"
  integrity sha512-Ofq76qKAPhxbiyzPf/5LPjJln26VTKwU9hIU0ACxQ6tNtBJ1CHmI7iITrdp7vNezhZc0FlkXwrIpqXjhBJZgLQ==
  dependencies:
    npm-registry-fetch "^13.0.0"

libnpmteam@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmjs.org/libnpmteam/-/libnpmteam-4.0.3.tgz"
  integrity sha512-LsYYLz4TlTpcqkusInY5MhKjiHFaCx1GV0LmydXJ/QMh+3IWBJpUhes4ynTZuFoJKkDIFjxyMU09ul+RZixgdg==
  dependencies:
    aproba "^2.0.0"
    npm-registry-fetch "^13.0.0"

libnpmversion@^3.0.1:
  version "3.0.6"
  resolved "https://registry.npmjs.org/libnpmversion/-/libnpmversion-3.0.6.tgz"
  integrity sha512-+lI+AO7cZwDxyAeWCIR8+n9XEfgSDAqmNbv4zy+H6onGthsk/+E3aa+5zIeBpyG5g268zjpc0qrBch0Q3w0nBA==
  dependencies:
    "@npmcli/git" "^3.0.0"
    "@npmcli/run-script" "^4.1.3"
    json-parse-even-better-errors "^2.3.1"
    proc-log "^2.0.0"
    semver "^7.3.7"

lie@~3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  integrity sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==
  dependencies:
    immediate "~3.0.5"

linkify-it@^3.0.1:
  version "3.0.3"
  resolved "https://registry.npmjs.org/linkify-it/-/linkify-it-3.0.3.tgz"
  integrity sha512-ynTsyrFSdE5oZ/O9GEf00kPngmOfVwazR5GKDq6EYfhlpFug3J2zybX56a2PRRpc9P+FuSoGNAwjlbDs9jJBPQ==
  dependencies:
    uc.micro "^1.0.1"

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==

loader-utils@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.2.tgz"
  integrity sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz"
  integrity sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ==

lodash.difference@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.difference/-/lodash.difference-4.5.0.tgz"
  integrity sha512-dS2j+W26TQ7taQBGN8Lbbq04ssV3emRw4NY58WErlTO29pIqS0HmoT5aJ9+TUQ1N3G+JOZSji4eugsWwGp9yPA==

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-4.4.0.tgz"
  integrity sha512-C5N2Z3DgnnKr0LOpv/hKCgKdb7ZZwafIrsesve6lmzvZIRZRGaZ/l6Q8+2W7NaT+ZwO3fFlSCzCzrDCFdJfZ4g==

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz"
  integrity sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  integrity sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  integrity sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  integrity sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz"
  integrity sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=

lodash.union@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.union/-/lodash.union-4.6.0.tgz"
  integrity sha512-c4pB2CdGrGdjMKYLA+XiRDO7Y0PRQbm/Gzg8qMj+QH+pFVAoTp5sBpO0odL3FjoPCGjK96p6qsP+yQoiLoOBcw==

lodash@^4.17.11, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.17.3:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-node@^8.0.3:
  version "8.0.3"
  resolved "https://registry.npmjs.org/log-node/-/log-node-8.0.3.tgz"
  integrity sha512-1UBwzgYiCIDFs8A0rM2QdBFo8Wd8UQ0HrSTu/MNI+/2zN3NoHRj2fhplurAyuxTYUXu3Oohugq1jAn5s05u1MQ==
  dependencies:
    ansi-regex "^5.0.1"
    cli-color "^2.0.1"
    cli-sprintf-format "^1.1.1"
    d "^1.0.1"
    es5-ext "^0.10.53"
    sprintf-kit "^2.0.1"
    supports-color "^8.1.1"
    type "^2.5.0"

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log@^6.0.0, log@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/log/-/log-6.3.1.tgz"
  integrity sha512-McG47rJEWOkXTDioZzQNydAVvZNeEkSyLJ1VWkFwfW+o1knW+QSi8D1KjPn/TnctV+q99lkvJNe1f0E1IjfY2A==
  dependencies:
    d "^1.0.1"
    duration "^0.2.2"
    es5-ext "^0.10.53"
    event-emitter "^0.3.5"
    sprintf-kit "^2.0.1"
    type "^2.5.0"
    uni-global "^1.0.0"

logform@^2.3.2, logform@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/logform/-/logform-2.4.1.tgz"
  integrity sha512-7XB/tqc3VRbri9pRjU6E97mQ8vC27ivJ3lct4jhyT+n0JNDd4YKldFl0D75NqDp46hk8RC7Ma1Vjv/UPf67S+A==
  dependencies:
    "@colors/colors" "1.5.0"
    fecha "^4.2.0"
    ms "^2.1.1"
    safe-stable-stringify "^2.3.1"
    triple-beam "^1.3.0"

long-timeout@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/long-timeout/-/long-timeout-0.1.1.tgz"
  integrity sha1-lyHXiLR+C8taJMLivuGg2lXatRQ=

lowercase-keys@^1.0.0, lowercase-keys@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
  integrity sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==

lowercase-keys@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-2.0.0.tgz"
  integrity sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.4.4, lru-cache@^7.5.1, lru-cache@^7.7.1:
  version "7.13.2"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-7.13.2.tgz#bb5d3f1deea3f3a7a35c1c44345566a612e09cd0"
  integrity sha512-VJL3nIpA79TodY/ctmZEfhASgqekbT574/c4j3jn4bKXbSCnTTCH/KltZyvL2GlV+tGSMtsWyem8DCX7qKTMBA==

lru-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/lru-queue/-/lru-queue-0.1.0.tgz"
  integrity sha512-BpdYkt9EvGl8OfWHDQPISVpcl5xZthb+XPsbELj5AQXxIC8IriDZIQYjBJPEm5rS420sjZ0TLEzRcq5KdBhYrQ==
  dependencies:
    es5-ext "~0.10.2"

luxon@^1.23.x:
  version "1.28.0"
  resolved "https://registry.npmjs.org/luxon/-/luxon-1.28.0.tgz"
  integrity sha512-TfTiyvZhwBYM/7QdAVDh+7dBTBA29v4ik0Ce9zda3Mnf8on1S5KJI8P2jKFZ8+5C0jhmr0KwJEO/Wdpm0VeWJQ==

make-dir@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz"
  integrity sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==
  dependencies:
    pify "^3.0.0"

make-dir@^3.0.0, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

make-fetch-happen@^10.0.3, make-fetch-happen@^10.0.6, make-fetch-happen@^10.1.8:
  version "10.2.0"
  resolved "https://registry.yarnpkg.com/make-fetch-happen/-/make-fetch-happen-10.2.0.tgz#0bde3914f2f82750b5d48c6d2294d2c74f985e5b"
  integrity sha512-OnEfCLofQVJ5zgKwGk55GaqosqKjaR6khQlJY3dBAA+hM25Bc5CmX5rKUfVut+rYA3uidA7zb7AvcglU87rPRg==
  dependencies:
    agentkeepalive "^4.2.1"
    cacache "^16.1.0"
    http-cache-semantics "^4.1.0"
    http-proxy-agent "^5.0.0"
    https-proxy-agent "^5.0.0"
    is-lambda "^1.0.1"
    lru-cache "^7.7.1"
    minipass "^3.1.6"
    minipass-collect "^1.0.2"
    minipass-fetch "^2.0.3"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    negotiator "^0.6.3"
    promise-retry "^2.0.1"
    socks-proxy-agent "^7.0.0"
    ssri "^9.0.0"

markdown-it@^12.2.0:
  version "12.3.2"
  resolved "https://registry.npmjs.org/markdown-it/-/markdown-it-12.3.2.tgz"
  integrity sha512-TchMembfxfNVpHkbtriWltGWc+m3xszaRD0CZup7GFFhzIgQqxIfn3eGj1yZpfuflzPvfkt611B2Q/Bsk1YnGg==
  dependencies:
    argparse "^2.0.1"
    entities "~2.1.0"
    linkify-it "^3.0.1"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

mdurl@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/mdurl/-/mdurl-1.0.1.tgz"
  integrity sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memoizee@^0.4.14, memoizee@^0.4.15:
  version "0.4.15"
  resolved "https://registry.npmjs.org/memoizee/-/memoizee-0.4.15.tgz"
  integrity sha512-UBWmJpLZd5STPm7PMUlOw/TSy972M+z8gcyQ5veOnSDRREz/0bmpyTfKt3/51DhEBqCZQn1udM/5flcSPYhkdQ==
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.53"
    es6-weak-map "^2.0.3"
    event-emitter "^0.3.5"
    is-promise "^2.2.2"
    lru-queue "^0.1.0"
    next-tick "^1.1.0"
    timers-ext "^0.1.7"

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@^1.1.2, methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

milliseconds@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/milliseconds/-/milliseconds-1.0.3.tgz"
  integrity sha512-O8fANvJPdQULWgsLW+jyyPMAw4qiiPHGQDF7lXQmOXLXbmjphetpOYBk5Tut/74Q3EZ66g7TbvXw5RRi1wnJPQ==

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2", mime-db@^1.28.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mime@2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz"
  integrity sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-response@^1.0.0, mimic-response@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz"
  integrity sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==

mimic-response@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz"
  integrity sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==

minimatch@^3.0.2, minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1, minimatch@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-5.1.0.tgz"
  integrity sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.3:
  version "9.0.5"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.3, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.6"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.6.tgz"
  integrity sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==

minipass-collect@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/minipass-collect/-/minipass-collect-1.0.2.tgz"
  integrity sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==
  dependencies:
    minipass "^3.0.0"

minipass-fetch@^2.0.3:
  version "2.1.0"
  resolved "https://registry.npmjs.org/minipass-fetch/-/minipass-fetch-2.1.0.tgz"
  integrity sha512-H9U4UVBGXEyyWJnqYDCLp1PwD8XIkJ4akNHp1aGVI+2Ym7wQMlxDKi4IB4JbmyU+pl9pEs/cVrK6cOuvmbK4Sg==
  dependencies:
    minipass "^3.1.6"
    minipass-sized "^1.0.3"
    minizlib "^2.1.2"
  optionalDependencies:
    encoding "^0.1.13"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/minipass-flush/-/minipass-flush-1.0.5.tgz"
  integrity sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==
  dependencies:
    minipass "^3.0.0"

minipass-json-stream@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/minipass-json-stream/-/minipass-json-stream-1.0.1.tgz"
  integrity sha512-ODqY18UZt/I8k+b7rl2AENgbWE8IDYam+undIJONvigAz8KR5GWblsFTEfQs0WODsjbSXWlm+JHEv8Gr6Tfdbg==
  dependencies:
    jsonparse "^1.3.1"
    minipass "^3.0.0"

minipass-pipeline@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz"
  integrity sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==
  dependencies:
    minipass "^3.0.0"

minipass-sized@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/minipass-sized/-/minipass-sized-1.0.3.tgz"
  integrity sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0:
  version "3.3.3"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.3.3.tgz"
  integrity sha512-N0BOsdFAlNRfmwMhjAsLVWOk7Ljmeb39iqFlsV1At+jqRhSUP9yeof8FyJu4imaJiSUp8vQebWD/guZwGQC8iA==
  dependencies:
    yallist "^4.0.0"

minipass@^3.1.1, minipass@^3.1.6:
  version "3.3.4"
  resolved "https://registry.yarnpkg.com/minipass/-/minipass-3.3.4.tgz#ca99f95dd77c43c7a76bf51e6d200025eee0ffae"
  integrity sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw==
  dependencies:
    yallist "^4.0.0"

minizlib@^2.1.1, minizlib@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp-infer-owner@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mkdirp-infer-owner/-/mkdirp-infer-owner-2.0.0.tgz"
  integrity sha512-sdqtiFt3lkOaYvTXSRIUjkIdPTcxgv5+fgqYE/5qgwdw12cOrAuzzgzvVExIkH/ul1oeHN3bCLOWSG3XOqbKKw==
  dependencies:
    chownr "^2.0.0"
    infer-owner "^1.0.4"
    mkdirp "^1.0.3"

mkdirp@^0.5.4:
  version "0.5.6"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

mkdirp@^1.0.3, mkdirp@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

moment@^2.29.3:
  version "2.29.3"
  resolved "https://registry.npmjs.org/moment/-/moment-2.29.3.tgz"
  integrity sha512-c6YRvhEo//6T2Jz/vVtYzqBzwvPT95JBQ+smCytzf7c50oMZRsR/a4w88aD34I+/QVSfnoAnSBFPJHItlOMJVw==

mongodb-connection-string-url@^2.5.2:
  version "2.5.2"
  resolved "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-2.5.2.tgz"
  integrity sha512-tWDyIG8cQlI5k3skB6ywaEA5F9f5OntrKKsT/Lteub2zgwSUlhqEN2inGgBTm8bpYJf8QYBdA/5naz65XDpczA==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb-core@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/mongodb-core/-/mongodb-core-3.2.7.tgz"
  integrity sha512-WypKdLxFNPOH/Jy6i9z47IjG2wIldA54iDZBmHMINcgKOUcWJh8og+Wix76oGd7EyYkHJKssQ2FAOw5Su/n4XQ==
  dependencies:
    bson "^1.1.1"
    require_optional "^1.0.1"
    safe-buffer "^5.1.2"
  optionalDependencies:
    saslprep "^1.0.0"

mongodb@4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/mongodb/-/mongodb-4.5.0.tgz"
  integrity sha512-A2l8MjEpKojnhbCM0MK3+UOGUSGvTNNSv7AkP1fsT7tkambrkkqN/5F2y+PhzsV0Nbv58u04TETpkaSEdI2zKA==
  dependencies:
    bson "^4.6.2"
    denque "^2.0.1"
    mongodb-connection-string-url "^2.5.2"
    socks "^2.6.2"
  optionalDependencies:
    saslprep "^1.0.3"

mongoose@^6.3.3:
  version "6.3.3"
  resolved "https://registry.npmjs.org/mongoose/-/mongoose-6.3.3.tgz"
  integrity sha512-bAGuf+6mXuVjKReNcOGjdI05y9g0JXnRpZ3/PBN3kVXIn3rbhbFwR/lPbuwtsBsWhlblMK8tieDeFAVzV6yhww==
  dependencies:
    bson "^4.6.2"
    kareem "2.3.5"
    mongodb "4.5.0"
    mpath "0.9.0"
    mquery "4.0.2"
    ms "2.1.3"
    sift "16.0.0"

morgan@^1.10.0:
  version "1.10.0"
  resolved "https://registry.yarnpkg.com/morgan/-/morgan-1.10.0.tgz#091778abc1fc47cd3509824653dae1faab6b17d7"
  integrity sha512-AbegBVI4sh6El+1gNwvD5YIck7nSA36weD7xvIxG4in80j/UoK8AEGaWnnz8v1GxonMCltmlNs5ZKbGvl9b1XQ==
  dependencies:
    basic-auth "~2.0.1"
    debug "2.6.9"
    depd "~2.0.0"
    on-finished "~2.3.0"
    on-headers "~1.0.2"

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/mquery/-/mquery-4.0.2.tgz"
  integrity sha512-oAVF0Nil1mT3rxty6Zln4YiD6x6QsUWYz927jZzjMxOK2aqmhEz5JQ7xmrKK7xRFA2dwV+YaOpKU/S+vfNqKxA==
  dependencies:
    debug "4.x"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz"
  integrity sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==

ms@2.1.2, ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3, ms@^2.0.0, ms@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

multer-s3@^2.0.1:
  version "2.10.0"
  resolved "https://registry.npmjs.org/multer-s3/-/multer-s3-2.10.0.tgz"
  integrity sha512-RZsiqG19C9gE82lB7v8duJ+TMIf70fWYHlIwuNcsanOH1ePBoPXZvboEQxEow9jUkk7WQsuyVA2TgriOuDrVrw==
  dependencies:
    file-type "^3.3.0"
    html-comment-regex "^1.1.2"
    run-parallel "^1.1.6"

multer@^1.4.5-lts.1:
  version "1.4.5-lts.1"
  resolved "https://registry.npmjs.org/multer/-/multer-1.4.5-lts.1.tgz"
  integrity sha512-ywPWvcDMeH+z9gQq5qYHCCy+ethsk4goepZ45GLD63fOu0YcNecQxi64nDs3qluZB+murG3/D4dJ7+dGctcCQQ==
  dependencies:
    append-field "^1.0.0"
    busboy "^1.0.0"
    concat-stream "^1.5.2"
    mkdirp "^0.5.4"
    object-assign "^4.1.1"
    type-is "^1.6.4"
    xtend "^4.0.0"

mute-stream@0.0.8, mute-stream@~0.0.4:
  version "0.0.8"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz"
  integrity sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==

native-promise-only@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmjs.org/native-promise-only/-/native-promise-only-0.8.1.tgz"
  integrity sha512-zkVhZUA3y8mbz652WrL5x0fB0ehrBkulWT3TomAQ9iDtyXZvzKeEA6GPxAItBYeNYl5yngKRX612qHOhvMkDeg==

ncjsm@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ncjsm/-/ncjsm-4.3.0.tgz"
  integrity sha512-oah6YGwb4Ern2alojiMFcjPhE4wvQBw1Ur/kUr2P0ovKdzaF5pCIsGjs0f2y+iZeej0/5Y6OOhQ8j30cTDMEGw==
  dependencies:
    builtin-modules "^3.2.0"
    deferred "^0.7.11"
    es5-ext "^0.10.53"
    es6-set "^0.1.5"
    ext "^1.6.0"
    find-requires "^1.0.0"
    fs2 "^0.3.9"
    type "^2.5.0"

negotiator@0.6.3, negotiator@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

neo-async@^2.6.0, neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

next-tick@1, next-tick@^1.0.0, next-tick@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/next-tick/-/next-tick-1.1.0.tgz"
  integrity sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==

node-dir@^0.1.17:
  version "0.1.17"
  resolved "https://registry.npmjs.org/node-dir/-/node-dir-0.1.17.tgz"
  integrity sha512-tmPX422rYgofd4epzrNoOXiE8XFZYOcCq1vD7MAXCDO+O+zndlA2ztdKKMa+EeuBG5tHETpr4ml4RGgpqDCCAg==
  dependencies:
    minimatch "^3.0.2"

node-fetch@^2.6.7:
  version "2.6.7"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz"
  integrity sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==
  dependencies:
    whatwg-url "^5.0.0"

node-gyp@^9.0.0:
  version "9.1.0"
  resolved "https://registry.yarnpkg.com/node-gyp/-/node-gyp-9.1.0.tgz#c8d8e590678ea1f7b8097511dedf41fc126648f8"
  integrity sha512-HkmN0ZpQJU7FLbJauJTHkHlSVAXlNGDAzH/VYFZGDOnFyn/Na3GlNJfkudmufOdS6/jNFhy88ObzL7ERz9es1g==
  dependencies:
    env-paths "^2.2.0"
    glob "^7.1.4"
    graceful-fs "^4.2.6"
    make-fetch-happen "^10.0.3"
    nopt "^5.0.0"
    npmlog "^6.0.0"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.2"
    which "^2.0.2"

node-releases@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.5.tgz"
  integrity sha512-U9h1NLROZTq9uE1SNffn6WuPDg8icmi3ns4rEl/oTfIle4iLjTliCzgTsbaIFMq/Xn078/lfY/BL0GWZ+psK4Q==

nodemon@^2.0.15:
  version "2.0.18"
  resolved "https://registry.npmjs.org/nodemon/-/nodemon-2.0.18.tgz"
  integrity sha512-uAvrKipi2zAz8E7nkSz4qW4F4zd5fs2wNGsTx+xXlP8KXqd9ucE0vY9wankOsPboeDyuUGN9vsXGV1pLn80l/A==
  dependencies:
    chokidar "^3.5.2"
    debug "^3.2.7"
    ignore-by-default "^1.0.1"
    minimatch "^3.0.4"
    pstree.remy "^1.1.8"
    semver "^5.7.1"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.5"
    update-notifier "^5.1.0"

nodemon@^3.0.1:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/nodemon/-/nodemon-3.1.0.tgz#ff7394f2450eb6a5e96fe4180acd5176b29799c9"
  integrity sha512-xqlktYlDMCepBJd43ZQhjWwMw2obW/JRvkrLxq5RCNcuDDX1DbcPT+qT1IlIIdf+DhnWs90JpTMe+Y5KxOchvA==
  dependencies:
    chokidar "^3.5.2"
    debug "^4"
    ignore-by-default "^1.0.1"
    minimatch "^3.1.2"
    pstree.remy "^1.1.8"
    semver "^7.5.3"
    simple-update-notifier "^2.0.0"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.5"

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz"
  integrity sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==
  dependencies:
    abbrev "1"

nopt@~1.0.10:
  version "1.0.10"
  resolved "https://registry.npmjs.org/nopt/-/nopt-1.0.10.tgz"
  integrity sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==
  dependencies:
    abbrev "1"

normalize-package-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-4.0.0.tgz"
  integrity sha512-m+GL22VXJKkKbw62ZaBBjv8u6IE3UI4Mh5QakIqs3fWiKe0Xyi6L97hakwZK41/LD4R/2ly71Bayx0NLMwLA/g==
  dependencies:
    hosted-git-info "^5.0.0"
    is-core-module "^2.8.1"
    semver "^7.3.5"
    validate-npm-package-license "^3.0.4"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-url@^4.1.0:
  version "4.5.1"
  resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-4.5.1.tgz"
  integrity sha512-9UZCFRHQdNrfTpGg8+1INIg93B6zE0aXMVFkw1WFwvO4SlZywU6aLg5Of0Ap/PgcbSw4LNxvMWXMeugwMCX0AA==

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz"
  integrity sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==

npm-audit-report@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/npm-audit-report/-/npm-audit-report-3.0.0.tgz"
  integrity sha512-tWQzfbwz1sc4244Bx2BVELw0EmZlCsCF0X93RDcmmwhonCsPMoEviYsi+32R+mdRvOWXolPce9zo64n2xgPESw==
  dependencies:
    chalk "^4.0.0"

npm-bundled@^1.1.1, npm-bundled@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/npm-bundled/-/npm-bundled-1.1.2.tgz"
  integrity sha512-x5DHup0SuyQcmL3s7Rx/YQ8sbw/Hzg0rj48eN0dV7hf5cmQq5PXIeioroH3raV1QC1yh3uTYuMThvEQF3iKgGQ==
  dependencies:
    npm-normalize-package-bin "^1.0.1"

npm-install-checks@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/npm-install-checks/-/npm-install-checks-5.0.0.tgz"
  integrity sha512-65lUsMI8ztHCxFz5ckCEC44DRvEGdZX5usQFriauxHEwt7upv1FKaQEmAtU0YnOAdwuNWCmk64xYiQABNrEyLA==
  dependencies:
    semver "^7.1.1"

npm-normalize-package-bin@^1.0.0, npm-normalize-package-bin@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz"
  integrity sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA==

npm-package-arg@^9.0.0, npm-package-arg@^9.0.1, npm-package-arg@^9.0.2, npm-package-arg@^9.1.0:
  version "9.1.0"
  resolved "https://registry.yarnpkg.com/npm-package-arg/-/npm-package-arg-9.1.0.tgz#a60e9f1e7c03e4e3e4e994ea87fff8b90b522987"
  integrity sha512-4J0GL+u2Nh6OnhvUKXRr2ZMG4lR8qtLp+kv7UiV00Y+nGiSxtttCyIRHCt5L5BNkXQld/RceYItau3MDOoGiBw==
  dependencies:
    hosted-git-info "^5.0.0"
    proc-log "^2.0.1"
    semver "^7.3.5"
    validate-npm-package-name "^4.0.0"

npm-packlist@^5.1.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/npm-packlist/-/npm-packlist-5.1.1.tgz"
  integrity sha512-UfpSvQ5YKwctmodvPPkK6Fwk603aoVsf8AEbmVKAEECrfvL8SSe1A2YIwrJ6xmTHAITKPwwZsWo7WwEbNk0kxw==
  dependencies:
    glob "^8.0.1"
    ignore-walk "^5.0.1"
    npm-bundled "^1.1.2"
    npm-normalize-package-bin "^1.0.1"

npm-pick-manifest@^7.0.0, npm-pick-manifest@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/npm-pick-manifest/-/npm-pick-manifest-7.0.1.tgz"
  integrity sha512-IA8+tuv8KujbsbLQvselW2XQgmXWS47t3CB0ZrzsRZ82DbDfkcFunOaPm4X7qNuhMfq+FmV7hQT4iFVpHqV7mg==
  dependencies:
    npm-install-checks "^5.0.0"
    npm-normalize-package-bin "^1.0.1"
    npm-package-arg "^9.0.0"
    semver "^7.3.5"

npm-profile@^6.1.0:
  version "6.2.1"
  resolved "https://registry.yarnpkg.com/npm-profile/-/npm-profile-6.2.1.tgz#975c31ec75a6ae029ab5b8820ffdcbae3a1e3d5e"
  integrity sha512-Tlu13duByHyDd4Xy0PgroxzxnBYWbGGL5aZifNp8cx2DxUrHSoETXtPKg38aRPsBWMRfDtvcvVfJNasj7oImQQ==
  dependencies:
    npm-registry-fetch "^13.0.1"
    proc-log "^2.0.0"

npm-registry-fetch@^13.0.0, npm-registry-fetch@^13.0.1, npm-registry-fetch@^13.1.1:
  version "13.3.0"
  resolved "https://registry.yarnpkg.com/npm-registry-fetch/-/npm-registry-fetch-13.3.0.tgz#0ce10fa4a699a1e70685ecf41bbfb4150d74231b"
  integrity sha512-10LJQ/1+VhKrZjIuY9I/+gQTvumqqlgnsCufoXETHAPFTS3+M+Z5CFhZRDHGavmJ6rOye3UvNga88vl8n1r6gg==
  dependencies:
    make-fetch-happen "^10.0.6"
    minipass "^3.1.6"
    minipass-fetch "^2.0.3"
    minipass-json-stream "^1.0.1"
    minizlib "^2.1.2"
    npm-package-arg "^9.0.1"
    proc-log "^2.0.0"

npm-registry-utilities@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/npm-registry-utilities/-/npm-registry-utilities-1.0.0.tgz"
  integrity sha512-9xYfSJy2IFQw1i6462EJzjChL9e65EfSo2Cw6kl0EFeDp05VvU+anrQk3Fc0d1MbVCq7rWIxeer89O9SUQ/uOg==
  dependencies:
    ext "^1.6.0"
    fs2 "^0.3.9"
    memoizee "^0.4.15"
    node-fetch "^2.6.7"
    semver "^7.3.5"
    type "^2.6.0"
    validate-npm-package-name "^3.0.0"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/npm-run-path/-/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

npm-user-validate@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/npm-user-validate/-/npm-user-validate-1.0.1.tgz"
  integrity sha512-uQwcd/tY+h1jnEaze6cdX/LrhWhoBxfSknxentoqmIuStxUExxjWd3ULMLFPiFUrZKbOVMowH6Jq2FRWfmhcEw==

npm@^8.13.2:
  version "8.13.2"
  resolved "https://registry.npmjs.org/npm/-/npm-8.13.2.tgz"
  integrity sha512-aS6q/QKxkw9mTX8gR7Ft38BcRkW1i+h3sI1yAFmfQ30Yl1a1G4ZX3oNGDzaLCilU5ThFZQBS1F4ZSZsrVxJ7HA==
  dependencies:
    "@isaacs/string-locale-compare" "^1.1.0"
    "@npmcli/arborist" "^5.0.4"
    "@npmcli/ci-detect" "^2.0.0"
    "@npmcli/config" "^4.1.0"
    "@npmcli/fs" "^2.1.0"
    "@npmcli/map-workspaces" "^2.0.3"
    "@npmcli/package-json" "^2.0.0"
    "@npmcli/run-script" "^4.1.5"
    abbrev "~1.1.1"
    archy "~1.0.0"
    cacache "^16.1.1"
    chalk "^4.1.2"
    chownr "^2.0.0"
    cli-columns "^4.0.0"
    cli-table3 "^0.6.2"
    columnify "^1.6.0"
    fastest-levenshtein "^1.0.12"
    glob "^8.0.1"
    graceful-fs "^4.2.10"
    hosted-git-info "^5.0.0"
    ini "^3.0.0"
    init-package-json "^3.0.2"
    is-cidr "^4.0.2"
    json-parse-even-better-errors "^2.3.1"
    libnpmaccess "^6.0.2"
    libnpmdiff "^4.0.2"
    libnpmexec "^4.0.2"
    libnpmfund "^3.0.1"
    libnpmhook "^8.0.2"
    libnpmorg "^4.0.2"
    libnpmpack "^4.0.2"
    libnpmpublish "^6.0.2"
    libnpmsearch "^5.0.2"
    libnpmteam "^4.0.2"
    libnpmversion "^3.0.1"
    make-fetch-happen "^10.1.8"
    minipass "^3.1.6"
    minipass-pipeline "^1.2.4"
    mkdirp "^1.0.4"
    mkdirp-infer-owner "^2.0.0"
    ms "^2.1.2"
    node-gyp "^9.0.0"
    nopt "^5.0.0"
    npm-audit-report "^3.0.0"
    npm-install-checks "^5.0.0"
    npm-package-arg "^9.0.2"
    npm-pick-manifest "^7.0.1"
    npm-profile "^6.1.0"
    npm-registry-fetch "^13.1.1"
    npm-user-validate "^1.0.1"
    npmlog "^6.0.2"
    opener "^1.5.2"
    pacote "^13.6.1"
    parse-conflict-json "^2.0.2"
    proc-log "^2.0.1"
    qrcode-terminal "^0.12.0"
    read "~1.0.7"
    read-package-json "^5.0.1"
    read-package-json-fast "^2.0.3"
    readdir-scoped-modules "^1.1.0"
    rimraf "^3.0.2"
    semver "^7.3.7"
    ssri "^9.0.1"
    tar "^6.1.11"
    text-table "~0.2.0"
    tiny-relative-date "^1.3.0"
    treeverse "^2.0.0"
    validate-npm-package-name "^4.0.0"
    which "^2.0.2"
    write-file-atomic "^4.0.1"

npmlog@^6.0.0, npmlog@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz"
  integrity sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==
  dependencies:
    are-we-there-yet "^3.0.0"
    console-control-strings "^1.1.0"
    gauge "^4.0.3"
    set-blocking "^2.0.0"

oauth-sign@^0.8.2:
  version "0.8.2"
  resolved "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz"
  integrity sha1-Rqarfwrq2N6unsBWV4C31O/rnUM=

object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-2.2.0.tgz"
  integrity sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw==

object-inspect@^1.9.0:
  version "1.12.0"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.0.tgz"
  integrity sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@1.4.0, once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

one-time@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/one-time/-/one-time-1.0.0.tgz"
  integrity sha512-5DXOiRKwuSEcQ/l0kGCF6Q3jcADFv5tSmRaJck/OqkVFcOzutB134KRSfF0xDrL39MNnqxbHBbUUcjZIhTgb2g==
  dependencies:
    fn.name "1.x.x"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

open@^7.4.2:
  version "7.4.2"
  resolved "https://registry.npmjs.org/open/-/open-7.4.2.tgz"
  integrity sha512-MVHddDVweXZF3awtlAS+6pgKLlm/JgxZ90+/NBurBoQctVOOB/zDdVjcyPzQ+0laDGbsWgrRkflI65sQeOgT9Q==
  dependencies:
    is-docker "^2.0.0"
    is-wsl "^2.1.1"

opener@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz"
  integrity sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==

optimist@^0.3.7:
  version "0.3.7"
  resolved "https://registry.npmjs.org/optimist/-/optimist-0.3.7.tgz"
  integrity sha512-TCx0dXQzVtSCg2OgY/bO9hjM9cV4XYx09TVK+s3+FhkjT6LovsLe+pPMzpWf+6yXK/hUizs2gUoTw3jHM0VaTQ==
  dependencies:
    wordwrap "~0.0.2"

ora@^5.4.1:
  version "5.4.1"
  resolved "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz"
  integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-filter-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/os-filter-obj/-/os-filter-obj-2.0.0.tgz#1c0b62d5f3a2442749a2d139e6dddee6e81d8d16"
  integrity sha512-uksVLsqG3pVdzzPvmAHpBK0wKxYItuzZr7SziusRPoz67tGV8rL1szZ6IdeUrbqLjGDwApBtN29eEE3IqGHOjg==
  dependencies:
    arch "^2.1.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==

p-cancelable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/p-cancelable/-/p-cancelable-1.1.0.tgz"
  integrity sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==

p-cancelable@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.1.1.tgz"
  integrity sha512-BZOr3nRQHOntUjTrH8+Lh54smKHoHyur8We1V8DSMVrl5A2malOOwuJRnKRDjSnkoeBh4at6BwEnb5I7Jl31wg==

p-event@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/p-event/-/p-event-4.2.0.tgz"
  integrity sha512-KXatOjCRXXkSePPb1Nbi0p0m+gQAwdlbhi4wQKJPI1HsMQS9g+Sqp2o+QHziPr7eYJyOZet836KoHEVM1mwOrQ==
  dependencies:
    p-timeout "^3.1.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  integrity sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

p-timeout@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/p-timeout/-/p-timeout-3.2.0.tgz"
  integrity sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==
  dependencies:
    p-finally "^1.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

package-json@^6.3.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/package-json/-/package-json-6.5.0.tgz"
  integrity sha512-k3bdm2n25tkyxcjSKzB5x8kfVxlMdgsbPr0GkZcwHsLpba6cBjqCt1KlcChKEvxHIcTB1FVMuwoijZ26xex5MQ==
  dependencies:
    got "^9.6.0"
    registry-auth-token "^4.0.0"
    registry-url "^5.0.0"
    semver "^6.2.0"

pacote@^13.0.3, pacote@^13.6.1:
  version "13.6.1"
  resolved "https://registry.npmjs.org/pacote/-/pacote-13.6.1.tgz"
  integrity sha512-L+2BI1ougAPsFjXRyBhcKmfT016NscRFLv6Pz5EiNf1CCFJFU0pSKKQwsZTyAQB+sTuUL4TyFyp6J1Ork3dOqw==
  dependencies:
    "@npmcli/git" "^3.0.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    "@npmcli/promise-spawn" "^3.0.0"
    "@npmcli/run-script" "^4.1.0"
    cacache "^16.0.0"
    chownr "^2.0.0"
    fs-minipass "^2.1.0"
    infer-owner "^1.0.4"
    minipass "^3.1.6"
    mkdirp "^1.0.4"
    npm-package-arg "^9.0.0"
    npm-packlist "^5.1.0"
    npm-pick-manifest "^7.0.0"
    npm-registry-fetch "^13.0.1"
    proc-log "^2.0.0"
    promise-retry "^2.0.1"
    read-package-json "^5.0.0"
    read-package-json-fast "^2.0.3"
    rimraf "^3.0.2"
    ssri "^9.0.0"
    tar "^6.1.11"

pako@~1.0.2:
  version "1.0.11"
  resolved "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

parse-conflict-json@^2.0.1, parse-conflict-json@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/parse-conflict-json/-/parse-conflict-json-2.0.2.tgz"
  integrity sha512-jDbRGb00TAPFsKWCpZZOT93SxVP9nONOSgES3AevqRq/CHvavEBvKAjxX9p5Y5F0RZLxH9Ufd9+RwtCsa+lFDA==
  dependencies:
    json-parse-even-better-errors "^2.3.1"
    just-diff "^5.0.1"
    just-diff-apply "^5.2.0"

parseqs@0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/parseqs/-/parseqs-0.0.6.tgz"
  integrity sha512-jeAGzMDbfSHHA091hr0r31eYfTig+29g3GKKE/PPbEQ65X0lmMwlEoqmhzu0iztID5uJpZsFlUPDP8ThPL7M8w==

parseuri@0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/parseuri/-/parseuri-0.0.6.tgz"
  integrity sha512-AUjen8sAkGgao7UyCX6Ahv0gIK2fABKmYjvP4xmy5JaKvcbTRueIqIPHLAfq30xJddqSE033IOMUSOMCcK3Sow==

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-loader@^1.0.10:
  version "1.0.12"
  resolved "https://registry.npmjs.org/path-loader/-/path-loader-1.0.12.tgz"
  integrity sha512-n7oDG8B+k/p818uweWrOixY9/Dsr89o2TkCm6tOTex3fpdo2+BFDgR+KpB37mGKBRsBAlR8CIJMFN0OEy/7hIQ==
  dependencies:
    native-promise-only "^0.8.1"
    superagent "^7.1.6"

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

path2@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/path2/-/path2-0.1.0.tgz"
  integrity sha512-TX+cz8Jk+ta7IvRy2FAej8rdlbrP0+uBIkP/5DTODez/AuL/vSb30KuAdDxGVREXzn8QfAiu5mJYJ1XjbOhEPA==

path@^0.12.7:
  version "0.12.7"
  resolved "https://registry.npmjs.org/path/-/path-0.12.7.tgz"
  integrity sha512-aXXC6s+1w7otVF9UletFkFcDsJeO7lSZBPUQhtb5O0xJe8LtYhj/GxldoL09bBj9+ZmE2hNoHqQSFMN5fikh4Q==
  dependencies:
    process "^0.11.1"
    util "^0.10.3"

peek-readable@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/peek-readable/-/peek-readable-4.1.0.tgz"
  integrity sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==

peek-readable@^5.1.3:
  version "5.3.1"
  resolved "https://registry.yarnpkg.com/peek-readable/-/peek-readable-5.3.1.tgz#9cc2c275cceda9f3d07a988f4f664c2080387dff"
  integrity sha512-GVlENSDW6KHaXcd9zkZltB7tCLosKB/4Hg0fqBJkAoBgYG2Tn1xtMgXtSUuMU9AK/gCm/tTdT8mgAeF4YNeeqw==

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz"
  integrity sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.2.0, pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz"
  integrity sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  integrity sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw==
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"
  integrity sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg==

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

postcss-selector-parser@^6.0.10:
  version "6.0.10"
  resolved "https://registry.yarnpkg.com/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz#79b61e2c0d1bfc2602d549e11d0876256f8df88d"
  integrity sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

prepend-http@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/prepend-http/-/prepend-http-2.0.0.tgz"
  integrity sha512-ravE6m9Atw9Z/jjttRUZ+clIXogdghyZAuWJ3qEzjT+jI/dL1ifAqhZeC5VHzQp1MSt1+jxKkFNemj/iO7tVUA==

pretty-format@^27.0.0, pretty-format@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz"
  integrity sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

prismjs@^1.25.0:
  version "1.28.0"
  resolved "https://registry.npmjs.org/prismjs/-/prismjs-1.28.0.tgz"
  integrity sha512-8aaXdYvl1F7iC7Xm1spqSaY/OJBpYW3v+KJ+F17iYxvdc8sfjW194COK5wVhMZX45tGteiBQgdvD/nhxcRwylw==

proc-log@^2.0.0, proc-log@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/proc-log/-/proc-log-2.0.1.tgz"
  integrity sha512-Kcmo2FhfDTXdcbfDH76N7uBYHINxc/8GW7UAVuVP9I+Va3uHSerrnKV6dLooga/gh7GlgzuCCr/eoldnL1muGw==

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

process-utils@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/process-utils/-/process-utils-4.0.0.tgz"
  integrity sha512-fMyMQbKCxX51YxR7YGCzPjLsU3yDzXFkP4oi1/Mt5Ixnk7GO/7uUTj8mrCHUwuvozWzI+V7QSJR9cZYnwNOZPg==
  dependencies:
    ext "^1.4.0"
    fs2 "^0.3.9"
    memoizee "^0.4.14"
    type "^2.1.0"

process@^0.11.1:
  version "0.11.10"
  resolved "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==

promise-all-reject-late@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/promise-all-reject-late/-/promise-all-reject-late-1.0.1.tgz"
  integrity sha512-vuf0Lf0lOxyQREH7GDIOUMLS7kz+gs8i6B+Yi8dC68a2sychGrHTJYghMBD6k7eUcH0H5P73EckCA48xijWqXw==

promise-call-limit@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/promise-call-limit/-/promise-call-limit-1.0.1.tgz"
  integrity sha512-3+hgaa19jzCGLuSCbieeRsu5C2joKfYn8pY6JAuXFRVfF4IO+L7UPpFWNTeWT9pM7uhskvbPPd/oEOktCn317Q==

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz"
  integrity sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==

promise-queue@^2.2.5:
  version "2.2.5"
  resolved "https://registry.npmjs.org/promise-queue/-/promise-queue-2.2.5.tgz"
  integrity sha512-p/iXrPSVfnqPft24ZdNNLECw/UrtLTpT3jpAAMzl/o5/rDsGCPo3/CQS2611flL6LkoEJ3oQZw7C8Q80ZISXRQ==

promise-retry@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz"
  integrity sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==
  dependencies:
    err-code "^2.0.2"
    retry "^0.12.0"

promzard@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/promzard/-/promzard-0.3.0.tgz"
  integrity sha512-JZeYqd7UAcHCwI+sTOeUDYkvEU+1bQ7iE0UT1MgB/tERkAPkesW46MrpIySzODi+owTjZtiF8Ay5j9m60KmMBw==
  dependencies:
    read "1"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==

pstree.remy@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz"
  integrity sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  integrity sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

pupa@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/pupa/-/pupa-2.1.1.tgz"
  integrity sha512-l1jNAspIBSFqbT+y+5FosojNpVpF94nlI+wDUpqP9enwOTfHx9f0gh5nB96vl+6yTpsJsypeNrwfzPrKuHB41A==
  dependencies:
    escape-goat "^2.0.0"

qrcode-terminal@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.12.0.tgz"
  integrity sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==

qs@6.10.3, qs@^6.10.3, qs@^6.5.1, qs@^6.9.1:
  version "6.10.3"
  resolved "https://registry.npmjs.org/qs/-/qs-6.10.3.tgz"
  integrity sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==
  dependencies:
    side-channel "^1.0.4"

qs@6.9.3:
  version "6.9.3"
  resolved "https://registry.npmjs.org/qs/-/qs-6.9.3.tgz"
  integrity sha512-EbZYNarm6138UKKq46tdx08Yo/q9ZhFoAXAI1meAFd2GtbRDhbZY2WQSICskT0c5q99aFzLG1D4nvTk9tqfXIw==

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

querystring@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/querystring/-/querystring-0.2.1.tgz"
  integrity sha512-wkvS7mL/JMugcup3/rMitHmd9ecIGd2lhFhK9N3UUQ450h66d1r3Y9nvXzQAW1Lq+wyx61k/1pfKS5KuKiyEbg==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz"
  integrity sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==

radix-router@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/radix-router/-/radix-router-3.0.1.tgz"
  integrity sha512-jpHXHgP+ZmVzEfmZ7WVRSvc/EqMoAqYuMtBsHd9s47Hs9Iy8FDJhkweMrDH0wmdxanLzVIWhq0UpomLXNpW8tg==

random-bytes@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/random-bytes/-/random-bytes-1.0.0.tgz"
  integrity sha1-T2ih3Arli9P7lYSMMDJNt11kNgs=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.5.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz"
  integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc@1.2.8, rc@^1.2.8:
  version "1.2.8"
  resolved "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz"
  integrity sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-is@^17.0.1:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  integrity sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==

read-cmd-shim@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-3.0.0.tgz"
  integrity sha512-KQDVjGqhZk92PPNRj9ZEXEuqg8bUobSKRw+q0YQ3TKI5xkce7bUJobL4Z/OtiEbAAv70yEpYIXp4iQ9L8oPVog==

read-package-json-fast@^2.0.2, read-package-json-fast@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-2.0.3.tgz"
  integrity sha512-W/BKtbL+dUjTuRL2vziuYhp76s5HZ9qQhd/dKfWIZveD0O40453QNyZhC0e63lqZrAQ4jiOapVoeJ7JrszenQQ==
  dependencies:
    json-parse-even-better-errors "^2.3.0"
    npm-normalize-package-bin "^1.0.1"

read-package-json@^5.0.0, read-package-json@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/read-package-json/-/read-package-json-5.0.1.tgz"
  integrity sha512-MALHuNgYWdGW3gKzuNMuYtcSSZbGQm94fAp16xt8VsYTLBjUSc55bLMKe6gzpWue0Tfi6CBgwCSdDAqutGDhMg==
  dependencies:
    glob "^8.0.1"
    json-parse-even-better-errors "^2.3.1"
    normalize-package-data "^4.0.0"
    npm-normalize-package-bin "^1.0.1"

read@1, read@^1.0.7, read@~1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/read/-/read-1.0.7.tgz"
  integrity sha512-rSOKNYUmaxy0om1BNjMN4ezNT6VKK+2xF4GBhc81mkH7L60i6dp8qPYrkndNLT3QPphoII3maL9PVC9XmhHwVQ==
  dependencies:
    mute-stream "~0.0.4"

readable-stream@^2.0.0, readable-stream@^2.0.5, readable-stream@^2.2.2, readable-stream@^2.3.0, readable-stream@^2.3.5, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.0, readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-web-to-node-stream@^3.0.0, readable-web-to-node-stream@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/readable-web-to-node-stream/-/readable-web-to-node-stream-3.0.2.tgz#5d52bb5df7b54861fd48d015e93a2cb87b3ee0bb"
  integrity sha512-ePeK6cc1EcKLEhJFt/AebMCLL+GgSKhuygrZ/GLaKZYEecIgIECf4UaUuaByiGtzckwR4ain9VzUh95T1exYGw==
  dependencies:
    readable-stream "^3.6.0"

readdir-glob@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/readdir-glob/-/readdir-glob-1.1.1.tgz"
  integrity sha512-91/k1EzZwDx6HbERR+zucygRFfiPl2zkIYZtv3Jjr6Mn7SkKcVct8aVO+sSRiGMc6fLf72du3d92/uY63YPdEA==
  dependencies:
    minimatch "^3.0.4"

readdir-scoped-modules@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/readdir-scoped-modules/-/readdir-scoped-modules-1.1.0.tgz"
  integrity sha512-asaikDeqAQg7JifRsZn1NJZXo9E+VwlyCfbkZhwyISinqk5zNS6266HS5kah6P0SaQKGF6SkNnZVHUzHFYxYDw==
  dependencies:
    debuglog "^1.0.1"
    dezalgo "^1.0.0"
    graceful-fs "^4.1.2"
    once "^1.3.0"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz"
  integrity sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==
  dependencies:
    resolve "^1.1.6"

rechoir@^0.7.0:
  version "0.7.1"
  resolved "https://registry.npmjs.org/rechoir/-/rechoir-0.7.1.tgz"
  integrity sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==
  dependencies:
    resolve "^1.9.0"

redis-commands@^1.2.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/redis-commands/-/redis-commands-1.7.0.tgz"
  integrity sha512-nJWqw3bTFy21hX/CPKHth6sfhZbdiHP6bTawSgQBlKOVRG7EZkfHbbHwQJnrE4vsQf0CMNE+3gJ4Fmm16vdVlQ==

redis-dump@^0.1.10:
  version "0.1.10"
  resolved "https://registry.npmjs.org/redis-dump/-/redis-dump-0.1.10.tgz"
  integrity sha512-LqPbH51eUZtW0fOlaliUK9J0B/FsLJBiUDLzZTplBgs69NvU15O34FGxtGuzzOcGbRVU2N0eXg4MvIf23QcR5Q==
  dependencies:
    async "^1.5.2"
    optimist "^0.3.7"
    redis "^2.6.2"

redis-parser@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/redis-parser/-/redis-parser-2.6.0.tgz"
  integrity sha512-9Hdw19gwXFBJdN8ENUoNVJFRyMDFrE/ZBClPicKYDPwNPJ4ST1TedAHYNSiGKElwh2vrmRGMoJYbVdJd+WQXIw==

redis@*, redis@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/redis/-/redis-4.1.0.tgz"
  integrity sha512-5hvJ8wbzpCCiuN1ges6tx2SAh2XXCY0ayresBmu40/SGusWHFW86TAlIPpbimMX2DFHOX7RN34G2XlPA1Z43zg==
  dependencies:
    "@redis/bloom" "1.0.2"
    "@redis/client" "1.1.0"
    "@redis/graph" "1.0.1"
    "@redis/json" "1.0.3"
    "@redis/search" "1.0.6"
    "@redis/time-series" "1.0.3"

redis@^2.6.2:
  version "2.8.0"
  resolved "https://registry.npmjs.org/redis/-/redis-2.8.0.tgz"
  integrity sha512-M1OkonEQwtRmZv4tEWF2VgpG0JWJ8Fv1PhlgT5+B+uNq2cA3Rt1Yt/ryoR+vQNOQcIEgdCdfH0jr3bDpihAw1A==
  dependencies:
    double-ended-queue "^2.1.0-0"
    redis-commands "^1.2.0"
    redis-parser "^2.6.0"

registry-auth-token@^4.0.0:
  version "4.2.2"
  resolved "https://registry.npmjs.org/registry-auth-token/-/registry-auth-token-4.2.2.tgz"
  integrity sha512-PC5ZysNb42zpFME6D/XlIgtNGdTl8bBOCw90xQLVMpzuuubJKYDWFAEuUNc+Cn8Z8724tg2SDhDRrkVEsqfDMg==
  dependencies:
    rc "1.2.8"

registry-url@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/registry-url/-/registry-url-5.1.0.tgz"
  integrity sha512-8acYXXTI0AkQv6RAOjE3vOaIXZkT9wo4LOFbBKYQEEnnMNBpKqdUrI6S4NT0KPIo/WVvJ5tE/X5LF/TQUf0ekw==
  dependencies:
    rc "^1.2.8"

request-compose@^1.2.1:
  version "1.2.3"
  resolved "https://registry.npmjs.org/request-compose/-/request-compose-1.2.3.tgz"
  integrity sha512-i2m8y3kEveoaAVTsTqig2LmWI10bUdakqzIVHkTEAK8kcsr4a/+iL93tsujsLaMiCZmnB1Osdk3WEMsB//H66A==

request-oauth@0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/request-oauth/-/request-oauth-0.0.3.tgz"
  integrity sha512-q7WdJlpIcPaIDac0FZSy/yH37FO3UkUuPDIsiLALiLjuC6vzvuKIU14YIC3Lm0wtQRXS9GqvSo/fCYj8n75xSw==
  dependencies:
    oauth-sign "^0.8.2"
    qs "^6.5.1"
    uuid "^3.2.1"

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

require_optional@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/require_optional/-/require_optional-1.0.1.tgz"
  integrity sha512-qhM/y57enGWHAe3v/NcwML6a3/vfESLe/sGM2dII+gEO0BpKRUkWZow/tyloNqJyN6kXSl3RyyM8Ll5D/sJP8g==
  dependencies:
    resolve-from "^2.0.0"
    semver "^5.1.0"

resolve-alpn@^1.0.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.2.1.tgz"
  integrity sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-2.0.0.tgz"
  integrity sha1-lICrIOlP+h2egKgEx+oUdhGWa1c=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve@^1.0.0, resolve@^1.1.6, resolve@^1.9.0:
  version "1.22.1"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz"
  integrity sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

responselike@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/responselike/-/responselike-1.0.2.tgz"
  integrity sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==
  dependencies:
    lowercase-keys "^1.0.0"

responselike@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/responselike/-/responselike-2.0.0.tgz"
  integrity sha512-xH48u3FTB9VsZw7R+vvgaKeLKzT6jOogbQhEe/jewwnZgzPcnyWui2Av6JpoYZF/91uueC+lqhWqeURw5/qhCw==
  dependencies:
    lowercase-keys "^2.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz"
  integrity sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rimraf@^2.6.1:
  version "2.7.1"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
  integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz"
  integrity sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==

run-parallel-limit@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/run-parallel-limit/-/run-parallel-limit-1.1.0.tgz"
  integrity sha512-jJA7irRNM91jaKc3Hcl1npHsFLOXOoTkPCUL1JEa1R82O2miplXXRaGdjW/KM/98YQWDhJLiSs793CnXfblJUw==
  dependencies:
    queue-microtask "^1.2.2"

run-parallel@^1.1.6, run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.5.5:
  version "7.5.5"
  resolved "https://registry.npmjs.org/rxjs/-/rxjs-7.5.5.tgz"
  integrity sha512-sy+H0pQofO95VDmFLzyaw9xNJU4KTRSwQIGM6+iG3SypAtCiLDzpeG8sJrNCWn2Up9km+KhkvTdbkrdy+yzZdw==
  dependencies:
    tslib "^2.1.0"

safe-buffer@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz"
  integrity sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==

safe-buffer@5.1.2, safe-buffer@^5.1.1, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-stable-stringify@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.3.1.tgz"
  integrity sha512-kYBSfT+troD9cDA85VDnHZ1rpHC50O0g1e6WlGHVCz/g+JS+9WKLj+XwFYyR8UbrZN8ll9HUpDAAddY58MGisg==

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

saslprep@^1.0.0, saslprep@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/saslprep/-/saslprep-1.0.3.tgz"
  integrity sha512-/MY/PEMbk2SuY5sScONwhUDsV2p77Znkb/q3nSVstq/yQzYJOH/Azh29p9oJLsl3LnQwSvZDKagDGBsBwSooag==
  dependencies:
    sparse-bitfield "^3.0.3"

sax@1.2.1, sax@>=0.6.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz"
  integrity sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==

schema-utils@^3.1.0, schema-utils@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
  integrity sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

seek-bzip@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npmjs.org/seek-bzip/-/seek-bzip-1.0.6.tgz"
  integrity sha512-e1QtP3YL5tWww8uKaOCQ18UxIT2laNBXHjV/S2WYCiK4udiv8lkG89KRIoCjUagnAmCBurjF4zEVX2ByBbnCjQ==
  dependencies:
    commander "^2.8.1"

semver-diff@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/semver-diff/-/semver-diff-3.1.1.tgz"
  integrity sha512-GX0Ix/CJcHyB8c4ykpHGIAvLyOwOobtM/8d+TQkAd81/bEjgPHrfba41Vpesr7jX/t8Uh+R3EX9eAS5be+jQYg==
  dependencies:
    semver "^6.3.0"

semver-regex@^4.0.5:
  version "4.0.5"
  resolved "https://registry.yarnpkg.com/semver-regex/-/semver-regex-4.0.5.tgz#fbfa36c7ba70461311f5debcb3928821eb4f9180"
  integrity sha512-hunMQrEy1T6Jr2uEVjrAIqjwWcQTgOAcIM52C8MY1EZSD3DDNft04XzvYKPqjED65bNVVko0YI38nYeEHCX3yw==

semver-truncate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/semver-truncate/-/semver-truncate-3.0.0.tgz#0e3b4825d4a4225d8ae6e7c72231182b42edba40"
  integrity sha512-LJWA9kSvMolR51oDE6PN3kALBNaUdkxzAGcexw8gjMA8xr5zUqK0JiR3CgARSqanYF3Z1YHvsErb1KDgh+v7Rg==
  dependencies:
    semver "^7.3.5"

semver@^5.1.0, semver@^5.5.0, semver@^5.6.0, semver@^5.7.1:
  version "5.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==

semver@^6.0.0, semver@^6.2.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^7.0.0, semver@^7.1.1, semver@^7.3.4, semver@^7.3.5, semver@^7.3.7:
  version "7.3.7"
  resolved "https://registry.npmjs.org/semver/-/semver-7.3.7.tgz"
  integrity sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.8:
  version "7.6.3"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.6.3.tgz#980f7b5550bc175fb4dc09403085627f9eb33143"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

semver@^7.5.3:
  version "7.6.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.6.0.tgz#1a46a4db4bffcccd97b743b5005c8325f23d4e2d"
  integrity sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==
  dependencies:
    lru-cache "^6.0.0"

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npmjs.org/send/-/send-0.18.0.tgz"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.0.tgz"
  integrity sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag==
  dependencies:
    randombytes "^2.1.0"

serve-favicon@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npmjs.org/serve-favicon/-/serve-favicon-2.5.0.tgz"
  integrity sha512-FMW2RvqNr03x+C0WxTyu6sOv21oOjkq5j8tjquWccwa6ScNyGFOGJVpuS1NmTVGBAHS07xnSKotgf2ehQmf9iA==
  dependencies:
    etag "~1.8.1"
    fresh "0.5.2"
    ms "2.1.1"
    parseurl "~1.3.2"
    safe-buffer "5.1.1"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

serverless@^3.1.1:
  version "3.19.0"
  resolved "https://registry.npmjs.org/serverless/-/serverless-3.19.0.tgz"
  integrity sha512-XqbZ+UhxLjnwnzOEMkecJd68C3P9g9fQGwhHkuQelni3hIjmLlzkVBx6wlxrIBRgAXE9RAllwZvCsi2jZ9h2Ww==
  dependencies:
    "@serverless/dashboard-plugin" "^6.2.2"
    "@serverless/platform-client" "^4.3.2"
    "@serverless/utils" "^6.6.0"
    ajv "^8.11.0"
    ajv-formats "^2.1.1"
    archiver "^5.3.1"
    aws-sdk "^2.1147.0"
    bluebird "^3.7.2"
    cachedir "^2.3.0"
    chalk "^4.1.2"
    child-process-ext "^2.1.1"
    ci-info "^3.3.1"
    cli-progress-footer "^2.3.2"
    d "^1.0.1"
    dayjs "^1.11.2"
    decompress "^4.2.1"
    dotenv "^10.0.0"
    dotenv-expand "^5.1.0"
    essentials "^1.2.0"
    ext "^1.6.0"
    fastest-levenshtein "^1.0.12"
    filesize "^8.0.7"
    fs-extra "^9.1.0"
    get-stdin "^8.0.0"
    globby "^11.1.0"
    got "^11.8.5"
    graceful-fs "^4.2.10"
    https-proxy-agent "^5.0.1"
    is-docker "^2.2.1"
    js-yaml "^4.1.0"
    json-cycle "^1.3.0"
    json-refs "^3.0.15"
    lodash "^4.17.21"
    memoizee "^0.4.15"
    micromatch "^4.0.5"
    node-fetch "^2.6.7"
    npm-registry-utilities "^1.0.0"
    object-hash "^2.2.0"
    open "^7.4.2"
    path2 "^0.1.0"
    process-utils "^4.0.0"
    promise-queue "^2.2.5"
    require-from-string "^2.0.2"
    semver "^7.3.7"
    signal-exit "^3.0.7"
    strip-ansi "^6.0.1"
    supports-color "^8.1.1"
    tar "^6.1.11"
    timers-ext "^0.1.7"
    type "^2.6.0"
    untildify "^4.0.0"
    uuid "^8.3.2"
    yaml-ast-parser "0.0.43"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  integrity sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz"
  integrity sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  integrity sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  integrity sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shelljs@^0.8.5:
  version "0.8.5"
  resolved "https://registry.npmjs.org/shelljs/-/shelljs-0.8.5.tgz"
  integrity sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shx@^0.3.4:
  version "0.3.4"
  resolved "https://registry.npmjs.org/shx/-/shx-0.3.4.tgz"
  integrity sha512-N6A9MLVqjxZYcVn8hLmtneQWIJtp8IKzMP4eMnx+nqkvXoqinUPCbUFLp2UcWTEIUONhlk0ewxr/jaVGlc+J+g==
  dependencies:
    minimist "^1.2.3"
    shelljs "^0.8.5"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

sift@16.0.0:
  version "16.0.0"
  resolved "https://registry.npmjs.org/sift/-/sift-16.0.0.tgz"
  integrity sha512-ILTjdP2Mv9V1kIxWMXeMTIRbOBrqKc4JAXmFMnFq3fKeyQ2Qwa3Dw1ubcye3vR+Y6ofA0b9gNDr/y2t6eUeIzQ==

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

simple-git@^3.7.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/simple-git/-/simple-git-3.9.0.tgz"
  integrity sha512-twG3rcFMYASct3mJcCP8KBtmtN7V2s6cdO5IXVAe/ffIu6TjdkdVnatR+PGmcirKXiK5y28eIdCVF0i3UQypfQ==
  dependencies:
    "@kwsites/file-exists" "^1.1.1"
    "@kwsites/promise-deferred" "^1.1.1"
    debug "^4.3.4"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

simple-update-notifier@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz#d70b92bdab7d6d90dfd73931195a30b6e3d7cebb"
  integrity sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==
  dependencies:
    semver "^7.5.3"

slash@3.0.0, slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socket.io-adapter@~1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-1.1.2.tgz"
  integrity sha512-WzZRUj1kUjrTIrUKpZLEzFZ1OLj5FwLlAFQs9kuZJzJi5DKdU7FsWc36SNmA8iDOtwBQyT8FkrriRM8vXLYz8g==

socket.io-client@2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/socket.io-client/-/socket.io-client-2.4.0.tgz"
  integrity sha512-M6xhnKQHuuZd4Ba9vltCLT9oa+YvTsP8j9NcEiLElfIg8KeYPyhWOes6x4t+LTAC8enQbE/995AdTem2uNyKKQ==
  dependencies:
    backo2 "1.0.2"
    component-bind "1.0.0"
    component-emitter "~1.3.0"
    debug "~3.1.0"
    engine.io-client "~3.5.0"
    has-binary2 "~1.0.2"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    socket.io-parser "~3.3.0"
    to-array "0.1.4"

socket.io-parser@*:
  version "4.2.0"
  resolved "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.0.tgz"
  integrity sha512-tLfmEwcEwnlQTxFB7jibL/q2+q8dlVQzj4JdRLJ/W/G1+Fu9VSxCx1Lo+n1HvXxKnM//dUuD0xgiA7tQf57Vng==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

socket.io-parser@~3.3.0:
  version "3.3.2"
  resolved "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-3.3.2.tgz"
  integrity sha512-FJvDBuOALxdCI9qwRrO/Rfp9yfndRtc1jSgVgV8FDraihmSP/MLGD5PEuJrNfjALvcQ+vMDM/33AWOYP/JSjDg==
  dependencies:
    component-emitter "~1.3.0"
    debug "~3.1.0"
    isarray "2.0.1"

socket.io-parser@~3.4.0:
  version "3.4.1"
  resolved "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-3.4.1.tgz"
  integrity sha512-11hMgzL+WCLWf1uFtHSNvliI++tcRUWdoeYuwIl+Axvwy9z2gQM+7nJyN3STj1tLj5JyIUH8/gpDGxzAlDdi0A==
  dependencies:
    component-emitter "1.2.1"
    debug "~4.1.0"
    isarray "2.0.1"

socket.io@^2.3.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/socket.io/-/socket.io-2.4.1.tgz"
  integrity sha512-Si18v0mMXGAqLqCVpTxBa8MGqriHGQh8ccEOhmsmNS3thNCGBwO8WGrwMibANsWtQQ5NStdZwHqZR3naJVFc3w==
  dependencies:
    debug "~4.1.0"
    engine.io "~3.5.0"
    has-binary2 "~1.0.2"
    socket.io-adapter "~1.1.0"
    socket.io-client "2.4.0"
    socket.io-parser "~3.4.0"

socks-proxy-agent@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-7.0.0.tgz"
  integrity sha512-Fgl0YPZ902wEsAyiQ+idGd1A7rSFx/ayC1CQVMw5P+EQx2V0SgpGtf6OKFhVjPflPUl9YMmEOnmfjCdMUsygww==
  dependencies:
    agent-base "^6.0.2"
    debug "^4.3.3"
    socks "^2.6.2"

socks@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/socks/-/socks-2.6.2.tgz"
  integrity sha512-zDZhHhZRY9PxRruRMR7kMhnf3I8hDs4S3f9RecfnGxvcBHQcKcIH/oUcEWffsfl1XxdYlA7nnlGbbTvPz9D8gA==
  dependencies:
    ip "^1.1.5"
    smart-buffer "^4.2.0"

sort-keys-length@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/sort-keys-length/-/sort-keys-length-1.0.1.tgz"
  integrity sha512-GRbEOUqCxemTAk/b32F2xa8wDTs+Z1QHOkbhJDQTvv/6G3ZkbJ+frYWsTcc7cBB3Fu4wy4XlLCuNtJuMn7Gsvw==
  dependencies:
    sort-keys "^1.0.0"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/sort-keys/-/sort-keys-1.1.2.tgz"
  integrity sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg==
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
  integrity sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==

source-map-support@^0.5.12, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.7.3:
  version "0.7.4"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.7.4.tgz#a9bbe705c9d8846f4e08ff6765acf0f1b0898656"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz"
  integrity sha1-/0rm5oZWBWuks+eSqzM004JzyhE=
  dependencies:
    memory-pager "^1.0.2"

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.1.tgz"
  integrity sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  integrity sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.11"
  resolved "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.11.tgz"
  integrity sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g==

split2@^3.1.1:
  version "3.2.2"
  resolved "https://registry.npmjs.org/split2/-/split2-3.2.2.tgz"
  integrity sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==
  dependencies:
    readable-stream "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

sprintf-kit@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/sprintf-kit/-/sprintf-kit-2.0.1.tgz"
  integrity sha512-2PNlcs3j5JflQKcg4wpdqpZ+AjhQJ2OZEo34NXDtlB0tIPG84xaaXhpA8XFacFiwjKA4m49UOYG83y3hbMn/gQ==
  dependencies:
    es5-ext "^0.10.53"

ssri@^9.0.0, ssri@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmjs.org/ssri/-/ssri-9.0.1.tgz"
  integrity sha512-o57Wcn66jMQvfHG1FlYbWeZWW/dHZhJXjpIcTfXldXEk5nz5lStPo3mK0OJQfGR3RbZUlbISexbljkJzuEj/8Q==
  dependencies:
    minipass "^3.1.1"

stack-trace@0.0.x:
  version "0.0.10"
  resolved "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz"
  integrity sha512-KGzahc7puUKkzyMt+IqAep+TVNbKP+k2Lmwhub39m1AsTSkaDutx56aDCo+HLDzf/D26BIHTJWNiTG1KAJiQCg==

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

stream-promise@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/stream-promise/-/stream-promise-3.2.0.tgz"
  integrity sha512-P+7muTGs2C8yRcgJw/PPt61q7O517tDHiwYEzMWo1GSBCcZedUMT/clz7vUNsSxFphIlJ6QUL4GexQKlfJoVtA==
  dependencies:
    "2-thenable" "^1.0.0"
    es5-ext "^0.10.49"
    is-stream "^1.1.0"

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.0.0, string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.2, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"
  integrity sha512-kwrX1y7czp1E69n2ajbG65mIo9dqvJ+8aBQXOGVxqwvNbsXdFM6Lq37dLAY3mknUwru8CfcCbfOLL/gMo+fi3g==
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-dirs@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/strip-dirs/-/strip-dirs-2.1.0.tgz"
  integrity sha512-JOCxOeKLm2CAS73y/U4ZeZPTkE+gNVCzKt7Eox84Iej1LT/2pTWYpZKJuxwQpvX1LiZb1xokNR7RLfuBAa7T3g==
  dependencies:
    is-natural-number "^4.0.1"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-json-comments@^2.0.0, strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  integrity sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==

strip-outer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/strip-outer/-/strip-outer-1.0.1.tgz"
  integrity sha512-k55yxKHwaXnpYGsOzg4Vl8+tDrWylxDEpknGjhTiZB8dFRU5rTo9CAzeycivxV3s+zlTKwrs6WxMxR95n26kwg==
  dependencies:
    escape-string-regexp "^1.0.2"

strip-outer@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/strip-outer/-/strip-outer-2.0.0.tgz#c45c724ed9b1ff6be5f660503791404f4714084b"
  integrity sha512-A21Xsm1XzUkK0qK1ZrytDUvqsQWict2Cykhvi0fBQntGG5JSprESasEyV1EZ/4CiR5WB5KjzLTrP/bO37B0wPg==

strtok3@^6.2.4:
  version "6.3.0"
  resolved "https://registry.npmjs.org/strtok3/-/strtok3-6.3.0.tgz"
  integrity sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^4.1.0"

strtok3@^7.0.0-alpha.9:
  version "7.1.1"
  resolved "https://registry.yarnpkg.com/strtok3/-/strtok3-7.1.1.tgz#f548fd9dc59d0a76d5567ff8c16be31221f29dfc"
  integrity sha512-mKX8HA/cdBqMKUr0MMZAFssCkIGoZeSCMXgnt79yKxNFguMLVFgRe6wB+fsL0NmoHDbeyZXczy7vEPSoo3rkzg==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^5.1.3"

style-loader@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npmjs.org/style-loader/-/style-loader-3.3.1.tgz"
  integrity sha512-GPcQ+LDJbrcxHORTRes6Jy2sfvK2kS6hpSfI/fXhPt+spVzxF6LJ1dHLN9zIGmVaaP044YKaIatFaufENRiDoQ==

superagent@^7.1.6:
  version "7.1.6"
  resolved "https://registry.npmjs.org/superagent/-/superagent-7.1.6.tgz"
  integrity sha512-gZkVCQR1gy/oUXr+kxJMLDjla434KmSOKbx5iGD30Ql+AkJQ/YlPKECJy2nhqOsHLjGHzoDTXNSjhnvWhzKk7g==
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.3"
    debug "^4.3.4"
    fast-safe-stringify "^2.1.1"
    form-data "^4.0.0"
    formidable "^2.0.1"
    methods "^1.1.2"
    mime "2.6.0"
    qs "^6.10.3"
    readable-stream "^3.6.0"
    semver "^7.3.7"

supports-color@^5.3.0, supports-color@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz"
  integrity sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0, supports-color@^8.1.1:
  version "8.1.1"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

tar-stream@^1.5.2:
  version "1.6.2"
  resolved "https://registry.npmjs.org/tar-stream/-/tar-stream-1.6.2.tgz"
  integrity sha512-rzS0heiNf8Xn7/mpdSVVSMAWAoy9bfb1WOTYC78Z0UQKeKa/CWS8FOq0lKGNa8DWKAn9gxjCvMLYc5PGXYlK2A==
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

tar-stream@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz"
  integrity sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

tar@^6.1.0, tar@^6.1.11, tar@^6.1.2:
  version "6.1.11"
  resolved "https://registry.npmjs.org/tar/-/tar-6.1.11.tgz"
  integrity sha512-an/KZQzQUkZCkuoAA64hM92X0Urb6VpRhAFllDzz44U2mcD5scmT3zBc4VgVpkugF580+DQn8eAFSyoQt0tznA==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^3.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

terser-webpack-plugin@^5.1.3:
  version "5.3.3"
  resolved "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.3.tgz"
  integrity sha512-Fx60G5HNYknNTNQnzQ1VePRuu89ZVYWfjRAeT5rITuCY/1b08s49e5kSQwHDirKZWuoKOBRFS98EUUoZ9kLEwQ==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.7"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"
    terser "^5.7.2"

terser@^5.7.2:
  version "5.14.1"
  resolved "https://registry.npmjs.org/terser/-/terser-5.14.1.tgz"
  integrity sha512-+ahUAE+iheqBTDxXhTisdA8hgvbEG1hHOQ9xmNjeUJSoi6DU/gMrKNcfZjHkyY6Alnuyc+ikYJaxxfHkT3+WuQ==
  dependencies:
    "@jridgewell/source-map" "^0.3.2"
    acorn "^8.5.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-hex@1.0.x:
  version "1.0.0"
  resolved "https://registry.npmjs.org/text-hex/-/text-hex-1.0.0.tgz"
  integrity sha512-uuVGNWzgJ4yhRaNSiubPY7OjISw4sw4E5Uv0wbjp+OzcbmVU/rsT8ujgcXJhn9ypzsgr5vlzpPqP+MBBKcGvbg==

text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

throat@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/throat/-/throat-5.0.0.tgz"
  integrity sha512-fcwX4mndzpLQKBS1DVYhGAcYaYt7vsHNIvQV+WXMvnow5cgjPphq5CaayLaGsjRdSCKZFNGt7/GYAuXaNOiYCA==

through@^2.3.6, through@^2.3.8:
  version "2.3.8"
  resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==

timers-ext@^0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/timers-ext/-/timers-ext-0.1.7.tgz"
  integrity sha512-b85NUNzTSdodShTIbky6ZF02e8STtVVfD+fu4aXXShEELpozH+bCpJLYMPZbsABN2wDH7fJpqIoXxJpzbf0NqQ==
  dependencies:
    es5-ext "~0.10.46"
    next-tick "1"

tiny-relative-date@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/tiny-relative-date/-/tiny-relative-date-1.3.0.tgz"
  integrity sha512-MOQHpzllWxDCHHaDno30hhLfbouoYlOI8YlMNtvKe1zXbjEVhbcEovQxvZrPvtiYW630GQDoMMarCnjfyfHA+A==

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

to-array@0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/to-array/-/to-array-0.1.4.tgz"
  integrity sha512-LhVdShQD/4Mk4zXNroIQZJC+Ap3zgLcDuwEdcmLv9CCO73NWockQDwyUnW/m8VX/EElfL6FcYx7EeutN4HJA6A==

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/to-buffer/-/to-buffer-1.1.1.tgz"
  integrity sha512-lx9B5iv7msuFYE3dytT+KE5tap+rNYw+K4jVkb9R/asAb+pbBSM17jtunHplhBe6RRJdZx3Pn2Jph24O32mOVg==

to-readable-stream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/to-readable-stream/-/to-readable-stream-1.0.0.tgz"
  integrity sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

token-types@^4.1.1:
  version "4.2.0"
  resolved "https://registry.npmjs.org/token-types/-/token-types-4.2.0.tgz"
  integrity sha512-P0rrp4wUpefLncNamWIef62J0v0kQR/GfDVji9WKY7GDCWy5YbVSrKUTam07iWPZQGy0zWNOfstYTykMmPNR7w==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    ieee754 "^1.2.1"

token-types@^5.0.0-alpha.2:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/token-types/-/token-types-5.0.1.tgz#aa9d9e6b23c420a675e55413b180635b86a093b4"
  integrity sha512-Y2fmSnZjQdDb9W4w4r1tswlMHylzWIeOKpx0aZH9BgGtACHhrk3OkT52AzwcuqTRBZtvvnTjDBh8eynMulu8Vg==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    ieee754 "^1.2.1"

touch@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/touch/-/touch-3.1.0.tgz"
  integrity sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==
  dependencies:
    nopt "~1.0.10"

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

traverse@^0.6.6:
  version "0.6.6"
  resolved "https://registry.npmjs.org/traverse/-/traverse-0.6.6.tgz"
  integrity sha512-kdf4JKs8lbARxWdp7RKdNzoJBhGUcIalSYibuGyHJbmk40pOysQ0+QPvlkCOICOivDWU2IJo2rkrxyTK2AH4fw==

tree-kill@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz"
  integrity sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==

treeverse@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/treeverse/-/treeverse-2.0.0.tgz"
  integrity sha512-N5gJCkLu1aXccpOTtqV6ddSEi6ZmGkh3hjmbu1IjcavJK4qyOVQmi0myQKM7z5jVGmD68SJoliaVrMmVObhj6A==

trim-repeated@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/trim-repeated/-/trim-repeated-1.0.0.tgz"
  integrity sha512-pkonvlKk8/ZuR0D5tLW8ljt5I8kmxp2XKymhepUeOdCEfKpZaktSArkLHZt76OB1ZvO9bssUsDty4SWhLvZpLg==
  dependencies:
    escape-string-regexp "^1.0.2"

trim-repeated@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/trim-repeated/-/trim-repeated-2.0.0.tgz#5d60556d6d40d9461b7c7e06c3ac20b6b1d50090"
  integrity sha512-QUHBFTJGdOwmp0tbOG505xAgOp/YliZP/6UgafFXYZ26WT1bvQmSMJUvkeVSASuJJHbqsFbynTvkd5W8RBTipg==
  dependencies:
    escape-string-regexp "^5.0.0"

triple-beam@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/triple-beam/-/triple-beam-1.3.0.tgz"
  integrity sha512-XrHUvV5HpdLmIj4uVMxHggLbFSZYIn7HEWsqePZcI50pco+MPqJ50wMGY794X7AOOhxOBAjbkqfAbEe/QMp2Lw==

ts-node-dev@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ts-node-dev/-/ts-node-dev-2.0.0.tgz"
  integrity sha512-ywMrhCfH6M75yftYvrvNarLEY+SUXtUvU8/0Z6llrHQVBx12GiFk5sStF8UdfE/yfzk9IAq7O5EEbTQsxlBI8w==
  dependencies:
    chokidar "^3.5.1"
    dynamic-dedupe "^0.3.0"
    minimist "^1.2.6"
    mkdirp "^1.0.4"
    resolve "^1.0.0"
    rimraf "^2.6.1"
    source-map-support "^0.5.12"
    tree-kill "^1.2.2"
    ts-node "^10.4.0"
    tsconfig "^7.0.0"

ts-node@^10.4.0, ts-node@^10.8.1:
  version "10.8.1"
  resolved "https://registry.npmjs.org/ts-node/-/ts-node-10.8.1.tgz"
  integrity sha512-Wwsnao4DQoJsN034wePSg5nZiw4YKXf56mPIAeD6wVmiv+RytNSWqc2f3fKvcUoV+Yn2+yocD71VOfQHbmVX4g==
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/tsconfig/-/tsconfig-7.0.0.tgz"
  integrity sha512-vZXmzPrL+EmC4T/4rVlT2jNVMWCi/O4DIiSj3UHg1OE5kCKbk4mfrXc6dZksLgRM/TZlKnousKH9bbTazUWRRw==
  dependencies:
    "@types/strip-bom" "^3.0.0"
    "@types/strip-json-comments" "0.0.30"
    strip-bom "^3.0.0"
    strip-json-comments "^2.0.0"

tslib@^1.11.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.1.0, tslib@^2.3.1:
  version "2.4.0"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz"
  integrity sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

type-is@^1.6.4, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/type/-/type-1.2.0.tgz"
  integrity sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==

type@^2.1.0, type@^2.5.0, type@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/type/-/type-2.6.0.tgz"
  integrity sha512-eiDBDOmkih5pMbo9OqsqPRGMljLodLcwd5XD5JbtNB0o89xZAwynY9EdCDsJU7LtcVCClu9DvM7/0Ep1hYX3EQ==

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  integrity sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==

typescript@^4.7.4:
  version "4.7.4"
  resolved "https://registry.npmjs.org/typescript/-/typescript-4.7.4.tgz"
  integrity sha512-C0WQT0gezHuw6AdY1M2jxUO83Rjf0HP7Sk1DtXj6j1EwkQNZrHAg2XPWlq62oqEhYvONq5pkC2Y9oPljWToLmQ==

uberproto@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/uberproto/-/uberproto-2.0.6.tgz"
  integrity sha512-68H97HffZoFaa3HFtpstahWorN9dSp5uTU6jo3GjIQ6JkJBR3hC2Nx/e/HFOoYHdUyT/Z1MRWfxN1EiQJZUyCQ==

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npmjs.org/uc.micro/-/uc.micro-1.0.6.tgz"
  integrity sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==

uglify-js@^3.1.4:
  version "3.16.1"
  resolved "https://registry.npmjs.org/uglify-js/-/uglify-js-3.16.1.tgz"
  integrity sha512-X5BGTIDH8U6IQ1TIRP62YC36k+ULAa1d59BxlWvPUJ1NkW5L3FwcGfEzuVvGmhJFBu0YJ5Ge25tmRISqCmLiRQ==

uid-safe@~2.1.5:
  version "2.1.5"
  resolved "https://registry.npmjs.org/uid-safe/-/uid-safe-2.1.5.tgz"
  integrity sha512-KPHm4VL5dDXKz01UuEd88Df+KzynaohSL9fBh096KWAxSKZQDI2uBrVqtvRM4rwrIrRRKsdLNML/lnaaVSRioA==
  dependencies:
    random-bytes "~1.0.0"

unbzip2-stream@^1.0.9:
  version "1.4.3"
  resolved "https://registry.npmjs.org/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz"
  integrity sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg==
  dependencies:
    buffer "^5.2.1"
    through "^2.3.8"

undefsafe@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz"
  integrity sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==

uni-global@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/uni-global/-/uni-global-1.0.0.tgz"
  integrity sha512-WWM3HP+siTxzIWPNUg7hZ4XO8clKi6NoCAJJWnuRL+BAqyFXF8gC03WNyTefGoUXYc47uYgXxpKLIEvo65PEHw==
  dependencies:
    type "^2.5.0"

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz"
  integrity sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz"
  integrity sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==
  dependencies:
    imurmurhash "^0.1.4"

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz"
  integrity sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.0.tgz"
  integrity sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

untildify@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/untildify/-/untildify-4.0.0.tgz"
  integrity sha512-KK8xQ1mkzZeg9inewmFVDNkg3l5LUhoq9kN6iWYB/CC9YMG8HA+c1Q8HwDe6dEX7kErrEVNVBO3fWsVq5iDgtw==

update-browserslist-db@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.4.tgz"
  integrity sha512-jnmO2BEGUjsMOe/Fg9u0oczOe/ppIDZPebzccl1yDWGLFP16Pa1/RM5wEoKYPG2zstNcDuAStejyxsOuKINdGA==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

update-notifier@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/update-notifier/-/update-notifier-5.1.0.tgz"
  integrity sha512-ItnICHbeMh9GqUy31hFPrD1kcuZ3rpxDZbf4KUDavXwS0bW5m7SLbDQpGX3UYr072cbrF5hFUs3r5tUsPwjfHw==
  dependencies:
    boxen "^5.0.0"
    chalk "^4.1.0"
    configstore "^5.0.1"
    has-yarn "^2.1.0"
    import-lazy "^2.1.0"
    is-ci "^2.0.0"
    is-installed-globally "^0.4.0"
    is-npm "^5.0.0"
    is-yarn-global "^0.3.0"
    latest-version "^5.1.0"
    pupa "^2.1.1"
    semver "^7.3.4"
    semver-diff "^3.1.1"
    xdg-basedir "^4.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

url-parse-lax@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/url-parse-lax/-/url-parse-lax-3.0.0.tgz"
  integrity sha512-NjFKA0DidqPa5ciFcSrXnAltTtzz84ogy+NebPvfEgAck0+TNg4UJ4IN+fB7zRZfbgUf0syOo9MDxFkDSMuFaQ==
  dependencies:
    prepend-http "^2.0.0"

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.npmjs.org/url/-/url-0.10.3.tgz"
  integrity sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

util@^0.10.3:
  version "0.10.4"
  resolved "https://registry.npmjs.org/util/-/util-0.10.4.tgz"
  integrity sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==
  dependencies:
    inherits "2.0.3"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz"
  integrity sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==

uuid@^3.2.1:
  version "3.4.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==

validate-npm-package-license@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate-npm-package-name@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-3.0.0.tgz"
  integrity sha512-M6w37eVCMMouJ9V/sdPGnC5H4uDr73/+xdq0FBLO3TFFX1+7wiUY6Es328NN+y43tmY+doUdN9g9J21vqB7iLw==
  dependencies:
    builtins "^1.0.3"

validate-npm-package-name@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-4.0.0.tgz"
  integrity sha512-mzR0L8ZDktZjpX4OB46KT+56MAhl4EIazWP/+G/HPGuvfdaqg4YsCdtOm6U9+LOFyYDoh4dpnpxZRB9MQQns5Q==
  dependencies:
    builtins "^5.0.0"

validate-typescript@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/validate-typescript/-/validate-typescript-4.0.2.tgz"
  integrity sha512-txymJnrBWyE6FVmKQUJMIGQqSj4IcgaX3gFsnraygorw5co2zItZ1ho5yVQFEfsp24qHYeMaUc2dhTrBvN7J8g==
  dependencies:
    chalk "^2.3.1"

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

walk-up-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/walk-up-path/-/walk-up-path-1.0.0.tgz"
  integrity sha512-hwj/qMDUEjCU5h0xr90KGCf0tg0/LgJbmOWgrWKYlcJZM7XvquvUJZ0G/HMGr7F7OQMOUuPHWP9JpriinkAlkg==

watchpack@^2.3.1:
  version "2.4.0"
  resolved "https://registry.npmjs.org/watchpack/-/watchpack-2.4.0.tgz"
  integrity sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wcwidth@^1.0.0, wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

webpack-cli@^4.9.1:
  version "4.10.0"
  resolved "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.10.0.tgz"
  integrity sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==
  dependencies:
    "@discoveryjs/json-ext" "^0.5.0"
    "@webpack-cli/configtest" "^1.2.0"
    "@webpack-cli/info" "^1.5.0"
    "@webpack-cli/serve" "^1.7.0"
    colorette "^2.0.14"
    commander "^7.0.0"
    cross-spawn "^7.0.3"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^2.2.0"
    rechoir "^0.7.0"
    webpack-merge "^5.7.3"

webpack-merge@^5.7.3:
  version "5.8.0"
  resolved "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.8.0.tgz"
  integrity sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q==
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

webpack-sources@^2.2.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.1.tgz"
  integrity sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==
  dependencies:
    source-list-map "^2.0.1"
    source-map "^0.6.1"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack@^5.64.2:
  version "5.73.0"
  resolved "https://registry.npmjs.org/webpack/-/webpack-5.73.0.tgz"
  integrity sha512-svjudQRPPa0YiOYa2lM/Gacw0r6PvxptHj4FuEKQ2kX05ZLkjbVc5MnPs6its5j7IZljnIqSVo/OsY2X0IpHGA==
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^0.0.51"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    acorn "^8.4.1"
    acorn-import-assertions "^1.7.6"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.9.3"
    es-module-lexer "^0.9.0"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.1.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.1.3"
    watchpack "^2.3.1"
    webpack-sources "^3.2.3"

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

widest-line@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/widest-line/-/widest-line-3.1.0.tgz"
  integrity sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==
  dependencies:
    string-width "^4.0.0"

wildcard@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/wildcard/-/wildcard-2.0.0.tgz"
  integrity sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==

winston-transport@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/winston-transport/-/winston-transport-4.5.0.tgz"
  integrity sha512-YpZzcUzBedhlTAfJg6vJDlyEai/IFMIVcaEZZyl3UXIl4gmqRpU7AE89AHLkbzLUsv0NVmw7ts+iztqKxxPW1Q==
  dependencies:
    logform "^2.3.2"
    readable-stream "^3.6.0"
    triple-beam "^1.3.0"

winston@^3.3.3:
  version "3.7.2"
  resolved "https://registry.npmjs.org/winston/-/winston-3.7.2.tgz"
  integrity sha512-QziIqtojHBoyzUOdQvQiar1DH0Xp9nF1A1y7NVy2DGEsz82SBDtOalS0ulTRGVT14xPX3WRWkCsdcJKqNflKng==
  dependencies:
    "@dabh/diagnostics" "^2.0.2"
    async "^3.2.3"
    is-stream "^2.0.0"
    logform "^2.4.0"
    one-time "^1.0.0"
    readable-stream "^3.4.0"
    safe-stable-stringify "^2.3.1"
    stack-trace "0.0.x"
    triple-beam "^1.3.0"
    winston-transport "^4.5.0"

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz"
  integrity sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz"
  integrity sha512-1tMA907+V4QmxV7dbRvb4/8MaRALK6q9Abid3ndMYnbyo8piisCmeONVqVSXqQA3KaP4SLt5b7ud6E2sqP8TFw==

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  integrity sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write-file-atomic@^4.0.0, write-file-atomic@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.1.tgz"
  integrity sha512-nSKUxgAbyioruk6hU87QzVbY279oYT6uiwgDoujth2ju4mJ+TZau7SQBhtbTmUyuNYTuXnSyRn66FV0+eCgcrQ==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@^7.5.3:
  version "7.5.8"
  resolved "https://registry.npmjs.org/ws/-/ws-7.5.8.tgz"
  integrity sha512-ri1Id1WinAX5Jqn9HejiGb8crfRio0Qgu8+MtL36rlTA6RLsMdWt1Az/19A2Qij6uSHUMphEFaTKa4WG+UNHNw==

ws@~7.4.2:
  version "7.4.6"
  resolved "https://registry.npmjs.org/ws/-/ws-7.4.6.tgz"
  integrity sha512-YmhHDO4MzaDLB+M9ym/mDA5z0naX8j7SIlT8f8z+I0VtzsRbekxEutHSme7NPS2qE8StCYQNUnfWdXta/Yu85A==

xdg-basedir@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-4.0.0.tgz"
  integrity sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==

xml2js@0.4.19:
  version "0.4.19"
  resolved "https://registry.npmjs.org/xml2js/-/xml2js-0.4.19.tgz"
  integrity sha512-esZnJZJOiJR9wWKMyuvSE1y6Dq5LCuJanqhxslH2bxM6duahNZ+HMpCLhBQGZkbX6xRf8x1Y2eJlgt2q3qo49Q==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~9.0.1"

xmlbuilder@~9.0.1:
  version "9.0.7"
  resolved "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-9.0.7.tgz"
  integrity sha512-7YXTQc3P2l9+0rjaUbLwMKRhtmwg1M1eDf6nag7urC7pIPYLD9W/jmzQ4ptRSUbodw5S0jfoGTflLemQibSpeQ==

xmlhttprequest-ssl@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.6.3.tgz"
  integrity sha512-3XfeQE/wNkvrIktn2Kf0869fC0BN6UpydVasGIeSm2B1Llihf7/0UfZM+eCkOw3P7bP4+qPgqhm7ZoxuJtFU0Q==

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

yallist@4.0.0, yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==

yaml-ast-parser@0.0.43:
  version "0.0.43"
  resolved "https://registry.npmjs.org/yaml-ast-parser/-/yaml-ast-parser-0.0.43.tgz"
  integrity sha512-2PTINUwsRqSd+s8XxKaJWQlUuEMHJQyEuh2edBbW8KNJz0SJPwUSD2zRWqezFEdN7IzAgeuYHFUCF7o8zRdZ0A==

yamljs@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/yamljs/-/yamljs-0.3.0.tgz"
  integrity sha512-C/FsVVhht4iPQYXOInoxUM/1ELSf9EsgKH34FofQOp6hwCPrW4vG4w5++TED3xRUo8gD7l0P1J1dLlDYzODsTQ==
  dependencies:
    argparse "^1.0.7"
    glob "^7.0.5"

yauzl@^2.4.2:
  version "2.10.0"
  resolved "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz"
  integrity sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yeast@0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/yeast/-/yeast-0.1.2.tgz"
  integrity sha512-8HFIh676uyGYP6wP13R/j6OJ/1HwJ46snpvzE7aHAN3Ryqh2yX6Xox2B4CUmTwwOIzlG3Bs7ocsP5dZH/R1Qbg==

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

zip-stream@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/zip-stream/-/zip-stream-4.1.0.tgz"
  integrity sha512-zshzwQW7gG7hjpBlgeQP9RuyPGNxvJdzR8SUM3QhxCnLjWN2E7j3dOvpeDcQoETfHx0urRS7EtmVToql7YpU4A==
  dependencies:
    archiver-utils "^2.1.0"
    compress-commons "^4.1.0"
    readable-stream "^3.6.0"
